resource "google_compute_instance" "openvpn" {
  name         = "openvpn-vm"
  machine_type = var.machine_type
  zone         = var.zone

  boot_disk {
    initialize_params {
      image = "ubuntu-2004-focal-v20230606"  # Ubuntu image for OpenVPN
      size  = 10  # Size of the boot disk in GB
    }
  }

  network_interface {
    network    = var.network_name
    subnetwork = var.subnet_name
    access_config {
      # Include this block to assign a public IP
    }
  }

  service_account {
    email  = var.service_account_email
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]
  }

  metadata_startup_script = <<-EOF
    #!/bin/bash
    # Install OpenVPN
    apt-get update
    apt-get install -y openvpn easy-rsa
    # OpenVPN configuration (this can be customized)
    echo 'OpenVPN server setup script'
    # Add your custom OpenVPN setup script here
  EOF

  tags = ["allow-ssh", "allow-vpn"]

  # Allow SSH access and VPN traffic
  metadata = {
    enable-oslogin = "TRUE"
  }
}

resource "google_compute_firewall" "allow_vpn" {
  name    = "allow-vpn"
  network = var.network_name

  allow {
    protocol = "udp"
    ports    = ["1194"]
  }

  target_tags = ["allow-vpn"]
  source_ranges = ["0.0.0.0/0"]  # Adjust this to restrict access if needed
}

resource "google_compute_firewall" "allow_ssh" {
  name    = "allow-ssh"
  network = var.network_name

  allow {
    protocol = "tcp"
    ports    = ["22"]
  }

  target_tags = ["allow-ssh"]
  source_ranges = ["0.0.0.0/0"]  # Adjust this to restrict access if needed
}
