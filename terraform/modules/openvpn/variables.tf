variable "machine_type" {
  description = "The machine type for the OpenVPN VM."
  type        = string
  default     = "e2-small"
}

variable "zone" {
  description = "The zone in which to launch the OpenVPN VM."
  type        = string
  default     = "asia-southeast2-a"
}

variable "network_name" {
  description = "The name of the VPC network where the VM will be deployed."
  type        = string
}

variable "subnet_name" {
  description = "The name of the subnet where the VM will be deployed."
  type        = string
}

variable "service_account_email" {
  description = "The service account email to attach to the VM."
  type        = string
}
