resource "google_compute_network" "vpc" {
  name                    = var.network_name
  auto_create_subnetworks  = false
}

resource "google_compute_subnetwork" "subnet" {
  name          = var.subnet_name
  ip_cidr_range = var.subnet_cidr
  region        = var.region
  network       = google_compute_network.vpc.id
  private_ip_google_access = true

secondary_ip_range {
    range_name    = "gke-pods"
    ip_cidr_range = var.cluster_cidr  # CIDR block for Kubernetes pods
  }

  secondary_ip_range {
    range_name    = "gke-services"
    ip_cidr_range = var.services_cidr  # CIDR block for Kubernetes services
  }

}
