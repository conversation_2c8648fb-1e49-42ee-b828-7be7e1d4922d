# modules/firewall/variables.tf
variable "name" {
  description = "Name of the firewall rule"
  type        = string
}

variable "network" {
  description = "The VPC network name"
  type        = string
}

variable "protocol" {
  description = "The protocol to allow (e.g., tcp)"
  type        = string
}

variable "ports" {
  description = "List of ports to allow"
  type        = list(string)
}

variable "source_ranges" {
  description = "CIDR ranges allowed by the rule (used for INGRESS)"
  type        = list(string)
  default     = []
}

variable "destination_ranges" {
  description = "CIDR ranges allowed by the rule (used for EGRESS)"
  type        = list(string)
  default     = []
}

variable "target_tags" {
  description = "Network tags that will apply the firewall rule"
  type        = list(string)
  default     = []
}

variable "description" {
  description = "Description of the firewall rule"
  type        = string
  default     = ""
}

variable "direction" {
  description = "Direction of the firewall rule, either INGRESS or EGRESS"
  type        = string
  default     = "INGRESS"
}
