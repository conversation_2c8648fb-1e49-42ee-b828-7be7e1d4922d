# modules/firewall/main.tf
resource "google_compute_firewall" "rule" {
  name    = var.name
  network = var.network
  direction = var.direction  # Specify direction (INGRESS or EGRESS)

  allow {
    protocol = var.protocol
    ports    = var.ports
  }

  # Use source_ranges for INGRESS rules
  source_ranges = var.direction == "INGRESS" ? var.source_ranges : null

  # Use destination_ranges for EGRESS rules
  destination_ranges = var.direction == "EGRESS" ? var.destination_ranges : null

  target_tags   = var.target_tags
  description   = var.description
}
