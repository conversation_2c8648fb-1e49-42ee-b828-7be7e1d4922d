# Reserve a static IP for staging only
resource "google_compute_address" "static_nat_ip" {
  count  = var.use_static_nat_ip ? 1 : 0
  name   = var.static_nat_ip_name
  region = var.region
}

resource "google_compute_router" "nat_router" {
  name    = var.router_name
  region  = var.region
  network = var.network
}

resource "google_compute_router_nat" "nat_gw" {
  name   = var.nat_name
  region = var.region
  router = google_compute_router.nat_router.name

 # Use MANUAL_ONLY for static NAT IPs, AUTO_ONLY for dynamic NAT IPs
  nat_ip_allocate_option = var.use_static_nat_ip ? "MANUAL_ONLY" : "AUTO_ONLY"
  nat_ips                = var.use_static_nat_ip ? [google_compute_address.static_nat_ip[0].self_link] : null

  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
}