variable "router_name" {
  description = "The name of the NAT router"
  type        = string
}

variable "nat_name" {
  description = "The name of the NAT gateway"
  type        = string
}

variable "network" {
  description = "The network where the NAT gateway will be created"
  type        = string
}

variable "region" {
  description = "The region where the NAT gateway will be created"
  type        = string
}


variable "use_static_nat_ip" {
  description = "Flag to enable/disable static NAT IP allocation"
  type        = bool
  default     = false
}

variable "static_nat_ip_name" {
  description = "Name of the static NAT IP address"
  type        = string
  default     = "static-nat-ip-staging"
}