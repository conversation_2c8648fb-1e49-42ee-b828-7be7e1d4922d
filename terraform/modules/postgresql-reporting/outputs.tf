output "reporting_instance_connection_name" {
  description = "The connection name of the Reporting PostgreSQL instance."
  value       = google_sql_database_instance.reporting_postgresql_instance.connection_name
}

output "reporting_instance_private_ip" {
  description = "The private IP address of the Reporting PostgreSQL instance."
  value       = google_sql_database_instance.reporting_postgresql_instance.private_ip_address
}

output "public_ip" {
  value = google_sql_database_instance.reporting_postgresql_instance.public_ip_address
  description = "Public IP address of the Reporting PostgreSQL instance."
}

