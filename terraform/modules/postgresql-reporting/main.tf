provider "google" {
  project = var.project_id
  region  = var.region
}

resource "google_sql_database_instance" "reporting_postgresql_instance" {
  name             = var.reporting_db_instance_name
  database_version = "POSTGRES_16"
  region           = var.region
  settings {
    tier            = var.tier
    disk_size       = var.disk_size
    disk_type       = var.disk_type
    disk_autoresize = true

    ip_configuration {
      ipv4_enabled    = true
      private_network = var.network_id
      dynamic "authorized_networks" {
        for_each = var.reporting_authorized_networks
        content {
          name  = authorized_networks.value.name
          value = authorized_networks.value.value
        }
      }
    }
    # ✅ Add this block to fix your issue:
    maintenance_window {
      day  = var.reporting_maintenance_window.day
      hour = var.reporting_maintenance_window.hour
    }
  }
}

resource "google_sql_database" "reporting_database" {
  name     = var.reporting_db_name
  instance = google_sql_database_instance.reporting_postgresql_instance.name
}

resource "google_sql_user" "reporting_users" {
  name     = var.reporting_db_user
  instance = google_sql_database_instance.reporting_postgresql_instance.name
  password = var.reporting_db_password
}
