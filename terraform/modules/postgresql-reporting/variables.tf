variable "project_id" {
  description = "The GCP project ID."
  type        = string
}

variable "region" {
  description = "The region where the instance will be created."
  type        = string
}

variable "network_id" {
  description = "The VPC network ID."
  type        = string
}

variable "tier" {
  description = "The machine tier for the Cloud SQL instance."
  type        = string
  default     = "db-custom-1-3840"  # Default to 4 vCPUs and 4 GB RAM
}

variable "disk_size" {
  description = "The initial storage size (in GB)."
  type        = number
  default     = 20
}

variable "disk_type" {
  description = "The disk type for the Cloud SQL instance (e.g., PD_HDD or PD_SSD)."
  type        = string
  default     = "PD_HDD"
}

variable "reporting_db_instance_name" {
  description = "The name of the Reporting PostgreSQL instance."
  type        = string
}

variable "reporting_db_name" {
  description = "The name of the database for the Reporting instance."
  type        = string
}

variable "reporting_db_user" {
  description = "The database user for the Reporting PostgreSQL instance."
  type        = string
}

variable "reporting_db_password" {
  description = "The password for the Reporting database user."
  type        = string
}

variable "reporting_authorized_networks" {
  description = "List of authorized networks"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "reporting_maintenance_window" {
  description = "Maintenance window for reporting Cloud SQL instance"
  type = object({
    day  = number
    hour = number
  })
}


