provider "google" {
  project = var.project_id
  region  = var.region
}

resource "google_sql_database_instance" "postgresql_instance" {
  name             = var.db_instance_name
  database_version = "POSTGRES_16"
  region           = var.region
  settings {
    tier                = "db-custom-1-3840"  # 1 vCPUs and 3.75 GB of memory
    disk_size           = 20                  # Set initial storage size (20 GB)
    disk_type           = "PD_HDD"            # Use standard disk (HDD) for storage
    disk_autoresize     = true                # Enable automatic storage resizing

    ip_configuration {
      ipv4_enabled       = false              # No public IPv4, use private IP
      private_network    = var.network_id     # Reference to your VPC network
      # ✅ Use main_authorized_networks
      dynamic "authorized_networks" {
        for_each = var.main_authorized_networks
        content {
          name  = authorized_networks.value.name
          value = authorized_networks.value.value
        }
      }
    }
   # ✅ Use main_database_flags
    dynamic "database_flags" {
      for_each = var.main_database_flags
      content {
        name  = database_flags.value.name
        value = database_flags.value.value
      }
    }
    # ✅ Maintenance window (Saturday 9 PM WIB = 14 UTC)
    maintenance_window {
      day  = var.main_maintenance_window.day
      hour = var.main_maintenance_window.hour
    }
    }
  }


resource "google_sql_database" "database" {
  name     = var.db_name
  instance = google_sql_database_instance.postgresql_instance.name
}

resource "google_sql_user" "users" {
  name     = var.db_user
  instance = google_sql_database_instance.postgresql_instance.name
  password = var.db_password
}
