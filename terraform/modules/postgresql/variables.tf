variable "project_id" {
  type        = string
  description = "The ID of the project in which to create the PostgreSQL instance."
}

variable "region" {
  type        = string
  description = "The region where the PostgreSQL instance will be deployed."
}

variable "db_instance_name" {
  type        = string
  description = "The name of the PostgreSQL instance."
  default     = "snapgreen-stg-db"
}

variable "db_name" {
  type        = string
  description = "The name of the PostgreSQL database."
  default     = "snapgreen"
}

variable "db_user" {
  type        = string
  description = "The username for the PostgreSQL database."
  default     = "snapgreen_app"
}


variable "db_password" {
  type        = string
  description = "The password for the PostgreSQL database."
  sensitive   = true
}

variable "network_id" {
  type        = string
  description = "The ID of the VPC network to use for the PostgreSQL instance."
  #default     = "projects/snapgreen-stg/global/networks/snapgreen-stg-vpc"
}

variable "authorized_network_name" {
  type        = string
  description = "The name of the authorized network for accessing the PostgreSQL instance."
  #default     = "snapgreen-stg-subnet"
}

variable "authorized_network_value" {
  type        = string
  description = "The CIDR block for the authorized network."
  #default     = "********/16"
}


variable "main_authorized_networks" {
  type = list(object({
    name  = string
    value = string
  }))
}

variable "main_database_flags" {
  type = list(object({
    name  = string
    value = string
  }))
}

variable "main_maintenance_window" {
  description = "Maintenance window for main Cloud SQL instance"
  type = object({
    day  = number
    hour = number
  })
}