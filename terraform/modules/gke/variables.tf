variable "node_machine_type" {
  description = "The machine type for GKE nodes"
  type        = string
  default     = "e2-small"  # 4 CPU cores
}

variable "disk_size_gb" {
  description = "The size of the disk in GB for each node"
  type        = number
  default     = 20  # 100 GB disk
}

variable "use_preemptible_nodes" {
  description = "Whether to use preemptible nodes"
  type        = bool
  default     = true
}

variable "cluster_name" {
  description = "The name of the GKE cluster"
  type        = string
}

variable "master_cidr" {
  description = "CIDR block for master authorized networks"
  type        = string
}

variable "network_id" {
  description = "The VPC network ID"
  type        = string
}

variable "subnet_id" {
  description = "The Subnet ID for the GKE cluster"
  type        = string
}

variable "cluster_cidr" {
  description = "CIDR block for the GKE cluster"
  type        = string
}

variable "services_cidr" {
  description = "CIDR block for GKE services"
  type        = string
}

variable "region" {
  description = "The region for the GKE cluster"
  type        = string
}

variable "node_count" {
  description = "Number of nodes in the GKE cluster"
  type        = number
  #default     = 5
}

# Minimum number of nodes for autoscaling
variable "min_node_count" {
  description = "The minimum number of nodes for autoscaling"
  type        = number
  #default     = 3
}

# Maximum number of nodes for autoscaling
variable "max_node_count" {
  description = "The maximum number of nodes for autoscaling"
  type        = number
  #default     = 5  # Adjust based on your scaling needs
}

variable "environment" {
  description = "Environment name to distinguish resources (e.g., snapgreen-stg, snapgreen-prods)"
  type        = string
}

variable "master_ipv4_cidr_block" {
  description = "The master IPv4 CIDR block for the GKE cluster"
  type        = string
}