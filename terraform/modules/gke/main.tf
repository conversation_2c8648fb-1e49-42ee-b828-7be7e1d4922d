resource "google_container_cluster" "gke_cluster" {
  name       = var.cluster_name
  location   = var.region
  network    = var.network_id
  subnetwork = var.subnet_id


  # Ensure deletion protection is set to false
  deletion_protection = false

  # Enable IP allocation policy for GKE
  ip_allocation_policy {
    cluster_secondary_range_name  = "gke-pods"
    services_secondary_range_name = "gke-services"
  }

  # Master authorized networks for GKE
  master_authorized_networks_config {
    cidr_blocks {
      cidr_block   = var.master_cidr
      display_name = "Master Access"
    }
  cidr_blocks {
      cidr_block   = "**************/32"
      display_name = "public"
    }
      cidr_blocks {
      cidr_block   = "0.0.0.0/0"
      display_name = "public_open"
    }
    

  }
  
  # Enable private cluster configuration (Disabling external endpoint)
  private_cluster_config {
    enable_private_nodes    = true  # Nodes will only have private IPs
    enable_private_endpoint = false # Disable external (public) control plane endpoint
    master_ipv4_cidr_block  = var.master_ipv4_cidr_block  # Subnet for the master control plane IP range (adjust if necessary)
  }

maintenance_policy {
  recurring_window {
    start_time = "2025-06-20T18:00:00Z"   # Friday 1 AM WIB (Saturday early morning)
    end_time   = "2025-06-21T06:00:00Z"   # Saturday 1 PM WIB
    recurrence = "FREQ=WEEKLY;BYDAY=FR"
  }
}


 # Specify the zones for GKE nodes
  node_locations = ["asia-southeast2-a"]  # Limit to one zone (or you can add multiple zones)
  
  # Node pool configuration with autoscaling
  node_pool {
    #name = "snapgreen-stg-gke"
    name = "${var.environment}-gke"

    node_config {
      machine_type = var.node_machine_type
      disk_size_gb = var.disk_size_gb
      oauth_scopes = [
        "https://www.googleapis.com/auth/cloud-platform",
      ]
      preemptible = var.use_preemptible_nodes
      # Add firewall tags here to allow SSH access
      tags = ["allow-ssh", "iap-access"]

  # Kubelet configuration
    kubelet_config {
      cpu_manager_policy                  = "none"   # Required: "none" or "static"
      cpu_cfs_quota                       = false    # Optional: TRUE or FALSE
      pod_pids_limit                      = 0        # Optional: Limit on pods per node (set to 0 for unlimited)
      insecure_kubelet_readonly_port_enabled = "FALSE" # Optional: Strongly recommended to set to FALSE
    }
    }

    # Enable autoscaling for the node pool
    autoscaling {
      min_node_count = var.min_node_count  # Minimum number of nodes
      max_node_count = var.max_node_count  # Maximum number of nodes
    }

    initial_node_count = var.node_count
  }

  
}