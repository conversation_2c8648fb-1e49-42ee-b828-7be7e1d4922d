# Inside ../../modules/registry/main.tf
resource "google_artifact_registry_repository" "artifact_repos" {
  for_each = { for repo in var.repositories : repo.repository_id => repo }

  project       = each.value.project_id
  location      = each.value.location
  repository_id = each.value.repository_id
  description   = each.value.description
  format        = each.value.format
}

# resource "google_project_service" "artifact_registry" {
#   project            = "snapgreen-stg"
#   service            = "artifactregistry.googleapis.com"
#   disable_on_destroy = false  # Prevents Terraform from disabling the service on destroy
# }


resource "google_project_service" "artifact_registry" {
  for_each = toset([for repo in var.repositories : repo.project_id])

  project            = each.value
  service            = "artifactregistry.googleapis.com"
  disable_on_destroy = false
}

