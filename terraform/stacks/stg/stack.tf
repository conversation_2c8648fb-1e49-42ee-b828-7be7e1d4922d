// TERRAMATE: GENERATED AUTOMATICALLY DO NOT EDIT

terraform {
  backend "remote" {
    organization = "snapgreen"
    workspaces {
      name = "snapgreen-terraform-staging"
    }
  }
}
provider "google" {
  project = var.project_id
  region  = var.region
}
module "network_staging" {
  cluster_cidr  = var.cluster_cidr
  network_name  = var.network_name
  region        = var.region
  services_cidr = var.services_cidr
  source        = "../../modules/network"
  subnet_cidr   = var.subnet_cidr
  subnet_name   = var.subnet_name
}
module "artifact_registry" {
  repositories = [
    {
      project_id    = var.project_id
      location      = "asia-southeast2"
      repository_id = "go"
      description   = "go app repository"
      format        = "DOCKER"
    },
    {
      project_id    = var.project_id
      location      = "asia-southeast2"
      repository_id = "node"
      description   = "Node app artifact"
      format        = "DOCKER"
    },
  ]
  source = "../../modules/registry"
}
module "ssh_firewall" {
  description = "Allow SSH traffic on port 22"
  name        = "allow-ssh"
  network     = var.network_name
  ports = [
    "22",
  ]
  protocol = "tcp"
  source   = "../../modules/firewall"
  source_ranges = [
    "0.0.0.0/0",
  ]
  target_tags = [
    "allow-ssh",
  ]
}
module "iap_ssh_firewall" {
  description = "Allow SSH via Cloud IAP"
  direction   = "INGRESS"
  name        = "allow-iap-ssh-gke"
  network     = module.network_staging.vpc_id
  ports = [
    "22",
  ]
  protocol = "tcp"
  source   = "../../modules/firewall"
  source_ranges = [
    "************/20",
  ]
  target_tags = [
    "iap-access",
  ]
}
module "gke_to_postgres_firewall" {
  description = "Allow GKE nodes to access PostgreSQL"
  name        = "allow-gke-to-postgres"
  network     = "snapgreen-stg-vpc"
  ports = [
    "5432",
  ]
  protocol = "tcp"
  source   = "../../modules/firewall"
  source_ranges = [
    "********/16",
  ]
  target_tags = [
  ]
}
module "egress_to_gke_api_firewall" {
  description = "Allow egress traffic to the GKE API (private endpoint)"
  destination_ranges = [
    "10.0.0.0/28",
  ]
  direction = "EGRESS"
  name      = "allow-egress-to-gke-api"
  network   = module.network_staging.vpc_id
  ports = [
    "443",
  ]
  protocol = "tcp"
  source   = "../../modules/firewall"
}
module "cloud_nat_gw" {
  nat_name           = "nat-gw-stg"
  network            = module.network_staging.vpc_id
  region             = var.region
  router_name        = "nat-router-stg"
  source             = "../../modules/cloud-nat"
  static_nat_ip_name = "static-nat-ip-staging"
  use_static_nat_ip  = true
}
module "gke_staging" {
  cluster_cidr             = "********/16"
  cluster_name             = var.cluster_name
  environment              = "snapgreen-stg"
  initial_node_count       = 1
  master_cidr              = "*********/28"
  master_ipv4_cidr_block   = "10.0.0.0/28"
  max_node_count           = var.max_node_count
  min_node_count           = var.min_node_count
  network_id               = module.network_staging.vpc_id
  node_count               = var.node_count
  node_machine_type        = var.node_machine_type
  region                   = var.region
  remove_default_node_pool = true
  services_cidr            = "********/20"
  source                   = "../../modules/gke"
  subnet_id                = module.network_staging.subnet_id
  use_preemptible_nodes    = var.use_preemptible_nodes
}
module "postgresql" {
  authorized_network_name  = "snapgreen-stg-subnet"
  authorized_network_value = "********/16"
  db_instance_name         = "snapgreen-stg-db"
  db_name                  = "snapgreen"
  db_password              = var.db_password
  db_user                  = "snapgreen_app"
  main_authorized_networks = var.main_authorized_networks
  main_database_flags      = var.main_database_flags
  main_maintenance_window  = var.main_maintenance_window
  network_id               = "projects/snapgreen-stg/global/networks/snapgreen-stg-vpc"
  project_id               = var.project_id
  region                   = var.region
  source                   = "../../modules/postgresql"
}
module "gcs" {
  buckets = {
    snapgreen-stg-backup = {
      lifecycle_age_days = 30
      location           = "asia-southeast2"
      storage_class      = "STANDARD"
    }
    snapgreen-stg-logs = {
      lifecycle_age_days = 90
      location           = "asia-southeast2"
      storage_class      = "NEARLINE"
    }
  }
  source = "../../modules/gcs"
}
