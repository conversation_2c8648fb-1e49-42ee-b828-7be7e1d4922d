stack {
  id          = "stg"
  description = "Staging environment for SnapGreen"
}

generate_hcl "stack.tf" {
  content {
    terraform {
      backend "remote" {
        organization = "snapgreen"
        workspaces {
          name = "snapgreen-terraform-staging"
        }
      }
    }

    provider "google" {
      project = var.project_id
      region  = var.region
    }

    module "network_staging" {
      cluster_cidr  = var.cluster_cidr
      network_name  = var.network_name
      region        = var.region
      services_cidr = var.services_cidr
      source        = "../../modules/network"
      subnet_cidr   = var.subnet_cidr
      subnet_name   = var.subnet_name
    }

  module "artifact_registry" {
  source        = "../../modules/registry"

  repositories = [
    {
      project_id    = var.project_id
      location      = "asia-southeast2"
      repository_id = "go"
      description   = "go app repository"
      format        = "DOCKER"
    },
    {
      project_id    = var.project_id
      location      = "asia-southeast2"
      repository_id = "node"
      description   = "Node app artifact"
      format        = "DOCKER"
    }
  ]
}


    module "ssh_firewall" {
      description = "Allow SSH traffic on port 22"
      name        = "allow-ssh"
      network     = var.network_name
      ports       = ["22"]
      protocol    = "tcp"
      source      = "../../modules/firewall"
      source_ranges = ["0.0.0.0/0"]
      target_tags  = ["allow-ssh"]
    }

    module "iap_ssh_firewall" {
      description = "Allow SSH via Cloud IAP"
      name        = "allow-iap-ssh-gke"
      network     = module.network_staging.vpc_id
      ports       = ["22"]
      direction   = "INGRESS"
      protocol    = "tcp"
      source      = "../../modules/firewall"
      source_ranges = ["************/20"]
      target_tags  = ["iap-access"]
    }

    module "gke_to_postgres_firewall" {
      source        = "../../modules/firewall"
      name          = "allow-gke-to-postgres"
      network       = "snapgreen-stg-vpc"
      protocol      = "tcp"
      ports         = ["5432"]
      source_ranges = ["********/16"]
      target_tags   = []  # Adjust if the PostgreSQL instance has specific tags, else leave empty
      description   = "Allow GKE nodes to access PostgreSQL"
    }

    module "egress_to_gke_api_firewall" {
      source            = "../../modules/firewall"
      name              = "allow-egress-to-gke-api"
      network           = module.network_staging.vpc_id  # Ensure this points to the correct VPC
      protocol          = "tcp"
      ports             = ["443"]
      direction         = "EGRESS"
      destination_ranges = ["10.0.0.0/28"]
      description       = "Allow egress traffic to the GKE API (private endpoint)"
    }

    module "cloud_nat_gw" {
      nat_name    = "nat-gw-stg"
      network     = module.network_staging.vpc_id
      region      = var.region
      router_name = "nat-router-stg"
      source      = "../../modules/cloud-nat"
      # Enable static IP for staging
      use_static_nat_ip   = true
      static_nat_ip_name  = "static-nat-ip-staging"
    }


    module "gke_staging" {
      cluster_cidr          = "********/16"
      cluster_name          = var.cluster_name
      master_cidr           = "*********/28"
      network_id            = module.network_staging.vpc_id
      node_count            = var.node_count
      min_node_count     = var.min_node_count
      max_node_count     = var.max_node_count
      node_machine_type     = var.node_machine_type
      region                = var.region
      services_cidr         = "********/20"
      source                = "../../modules/gke"
      subnet_id             = module.network_staging.subnet_id
      use_preemptible_nodes = var.use_preemptible_nodes
      master_ipv4_cidr_block = "10.0.0.0/28"       # Unique range for staging
      environment            = "snapgreen-stg"     # Environment-specific name
      remove_default_node_pool = true
      initial_node_count       = 1
      
      #  Node pool with 1 vCPU, 4GB RAM, and 3 nodes
      # node_pools = {
      #   stg-pool = {
      #   machine_type   = "e2-custom-1-4096"
      #   disk_size_gb   = 20
      #   preemptible    = true
      #   node_count     = 0
      #   min_node_count = 0
      #   max_node_count = 3
      #    }
      #  }
       
      
    }
    module "postgresql" {
      source           = "../../modules/postgresql"
      project_id       = var.project_id
      region           = var.region
      db_instance_name = "snapgreen-stg-db"
      db_name          = "snapgreen"
      db_user          = "snapgreen_app"
      db_password      = var.db_password
      network_id       = "projects/snapgreen-stg/global/networks/snapgreen-stg-vpc"
      authorized_network_name = "snapgreen-stg-subnet"
      authorized_network_value = "********/16"
      main_authorized_networks   = var.main_authorized_networks
      main_database_flags         = var.main_database_flags
      main_maintenance_window = var.main_maintenance_window

    }

    module "gcs" {
      buckets = {
        snapgreen-stg-backup = {
          lifecycle_age_days = 30
          location           = "asia-southeast2"
          storage_class      = "STANDARD"
        }
        snapgreen-stg-logs = {
          lifecycle_age_days = 90
          location           = "asia-southeast2"
          storage_class      = "NEARLINE"
        }
      }
      source = "../../modules/gcs"
    }
  }
}
