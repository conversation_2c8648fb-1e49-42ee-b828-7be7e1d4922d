# Project and region
project_id     = "snapgreen-stg"
region         = "asia-southeast2"

# Network configuration
network_name   = "snapgreen-stg-vpc"
subnet_name    = "snapgreen-stg-subnet"
subnet_cidr    = "********/16"

# GKE cluster configuration
cluster_name          = "gke-stg-cluster"
node_count            = 6
min_node_count        = 1
max_node_count        = 5

node_machine_type     = "e2-medium"  # 
disk_size_gb          = 20              # 
use_preemptible_nodes = false
master_cidr           = "*********/28"
master_ipv4_cidr_block = "10.0.0.0/28"


# CIDR blocks for GKE
cluster_cidr  = "********/16"  # CIDR block for Kubernetes pods
services_cidr = "********/20"   # CIDR block for Kubernetes services

# PostgreSQL Database Configuration
db_instance_name = "snapgreen-stg-db"
db_name          = "snapgreen"
db_user          = "snapgreen_app"
#db password export variabel
#export TF_VAR_db_password="SN@pgreen2024"

# Network Configuration
# network_id               = "projects/snapgreen-stg/global/networks/snapgreen-stg-vpc"
# authorized_network_name  = "snapgreen-stg-subnet"
# authorized_network_value = "********/16"  # Your subnet CIDR block

environment = "snapgreen-stg"
authorized_network_value = "********/16"
network_id = "projects/snapgreen-stg/global/networks/snapgreen-stg-vpc"
authorized_network_name = "snapgreen-stg-subnet"

main_database_flags = [
  { name = "cloudsql.logical_decoding", value = "on" },
  { name = "max_replication_slots",     value = "10" },
  { name = "max_wal_senders",           value = "10" }
]

main_authorized_networks = [
  { name = "gke pod superset",  value = "34.50.64.55" }
]

main_maintenance_window = {
  day  = 6     # Saturday
  hour = 14    # 9:00 PM WIB
  update_track = "stable"
}