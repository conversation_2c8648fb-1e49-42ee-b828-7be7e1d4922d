# Project and region
project_id     = "snapgreen-prods"
region         = "asia-southeast2"

# Network configuration
network_name   = "snapgreen-prods-vpc"
subnet_name    = "snapgreen-prods-subnet"
subnet_cidr    = "********/16"  # Non-overlapping CIDR for the production project
# GKE cluster configuration
cluster_name          = "gke-prod-cluster"
node_count            = 9 # if you edit manually number of nodes from console gke u need to make sure node couunt on terraform also change it
min_node_count        = 3
max_node_count        = 9

node_machine_type     = "e2-medium"
disk_size_gb          = 20
use_preemptible_nodes = false
master_cidr           = "*********/28"  # Non-overlapping with staging and infra
master_ipv4_cidr_block = "*********/28"


# CIDR blocks for GKE
cluster_cidr  = "********/16"  # CIDR block for Kubernetes pods (production project)
services_cidr = "********/20"  # CIDR block for Kubernetes services (production project)

# PostgreSQL Database Configuration
db_instance_name = "snapgreen-prods-db"
db_name          = "snapgreen"
db_user          = "snapgreen_prod_app"
#export TF_VAR_db_password="SN@pgreen2024_pr0d"


# Reporting PostgreSQL Database Configuration
reporting_db_instance_name = "snapgreen-prods-reporting-db"  # Unique instance name
reporting_db_name          = "snapgreen_reporting_db"                 # Unique database name
reporting_db_user          = "snapgreen_reporting_prod"           # Unique user for Reporting database
reporting_db_password      = "reporting.SN@pgreen2024_pr0d"    # Secure password for Reporting user


environment = "snapgreen-prods"
authorized_network_value = "********/16"
network_id = "projects/snapgreen-prods/global/networks/snapgreen-prods-vpc"
authorized_network_name = "snapgreen-stg-subnet"
# editing configurasi

database_flags = [
  { name = "cloudsql.logical_decoding", value = "on" },
  { name = "max_replication_slots",     value = "10" },
  { name = "max_wal_senders",           value = "10" }
]


reporting_authorized_networks = [
  { name = "gke-prod",         value = "34.50.64.55" },
  { name = "looker-studio1",   value = "142.251.74.0/23" },
  { name = "looker-studio2",   value = "142.251.74.0/23" },
  { name = "looker-studio3",   value = "35.184.0.0/13" },
  { name = "looker-studio4",   value = "35.208.0.0/13" },
  { name = "looker-studio5",   value = "34.64.0.0/10" },
  { name = "looker-studio6",   value = "35.192.0.0/14" },
  { name = "ukirama",          value = "0.0.0.0/0" }
]


main_authorized_networks = [
  { name = "gke-production",  value = "34.50.64.55" },
  { name = "lokerstudio",     value = "35.192.0.0/14" },
  { name = "lookerstudio0",   value = "34.64.0.0/10" },
  { name = "lookerstudio1",   value = "35.184.0.0/13" },
  { name = "lookerstudio2",   value = "35.208.0.0/13" },
  { name = "lookerstudio3",   value = "74.125.0.0/16" },
  { name = "lookerstudio4",   value = "142.251.74.0/23" }
]

main_database_flags = [
  { name = "cloudsql.logical_decoding", value = "on" },
  { name = "max_replication_slots",     value = "10" },
  { name = "max_wal_senders",           value = "10" }
]

# snapgreen-prods-reporting-db (e.g., Sunday 2AM WIB = Saturday 19:00 UTC)
reporting_maintenance_window = {
  day  = 6     # Saturday
  hour = 14    # 9:00 PM WIB
}

# snapgreen-prods-db (Saturday 9PM WIB = 14:00 UTC)
main_maintenance_window = {
  day  = 6     # Saturday
  hour = 14    # 9:00 PM WIB
}