variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The GCP region for resources"
  type        = string
}

variable "network_name" {
  description = "The name of the VPC network"
  type        = string
}

variable "subnet_name" {
  description = "The name of the subnet"
  type        = string
}

variable "subnet_cidr" {
  description = "The CIDR block for the subnet"
  type        = string
}


variable "cluster_name" {
  description = "The name of the GKE cluster"
  type        = string
}

variable "node_count" {
  description = "The number of nodes in the GKE cluster"
  type        = number
}

variable "node_machine_type" {
  description = "The machine type for the GKE nodes"
  type        = string
}

variable "use_preemptible_nodes" {
  description = "Whether to use preemptible nodes"
  type        = bool
  default     = false
}

variable "master_cidr" {
  description = "CIDR block for master authorized networks"
  type        = string
}

variable "disk_size_gb" {
  description = "The size of the disk in GB for each node"
  type        = number
  default     = 50  # 100 GB disk
}

variable "cluster_cidr" {
  description = "CIDR block for the GKE cluster"
  type        = string
}

variable "services_cidr" {
  description = "CIDR block for GKE services"
  type        = string
}

variable "availability_type" {
  description = "The availability type for the PostgreSQL instance (ZONAL or REGIONAL)"
  type        = string
  default     = "ZONAL"
}

variable "ipv4_enabled" {
  description = "Enable public IP for the instance"
  type        = bool
  default     = false
}

# Minimum number of nodes for autoscaling
variable "min_node_count" {
  description = "The minimum number of nodes for autoscaling"
  type        = number
  default     = 3
}

# Maximum number of nodes for autoscaling
variable "max_node_count" {
  description = "The maximum number of nodes for autoscaling"
  type        = number
  default     = 5  # Adjust based on your scaling needs
}

variable "db_password" {
  type        = string
  description = "The password for the PostgreSQL database."
  sensitive   = true
}

variable "db_instance_name" {
  type        = string
  description = "The name of the PostgreSQL instance."
  default     = "snapgreen-stg-db"
}

variable "db_name" {
  type        = string
  description = "The name of the PostgreSQL database."
  default     = "snapgreen"
}

variable "db_user" {
  type        = string
  description = "The username for the PostgreSQL database."
  default     = "snapgreen_app"
}

variable "destination_ranges" {
  description = "CIDR ranges allowed by the rule (used for EGRESS)"
  type        = list(string)
  default     = []
}

variable "target_tags" {
  description = "Network tags that will apply the firewall rule"
  type        = list(string)
  default     = []
}

variable "description" {
  description = "Description of the firewall rule"
  type        = string
  default     = ""
}

variable "direction" {
  description = "Direction of the firewall rule, either INGRESS or EGRESS"
  type        = string
  default     = "INGRESS"
}

variable "source_ranges" {
  description = "CIDR ranges allowed by the rule (used for INGRESS)"
  type        = list(string)
  default     = []
}

variable "environment" {
  description = "Environment name to distinguish resources (e.g., snapgreen-stg, snapgreen-prods)"
  type        = string
}

variable "master_ipv4_cidr_block" {
  description = "The master IPv4 CIDR block for the GKE cluster"
  type        = string
}

variable "authorized_network_name" {
  type        = string
  description = "The name of the authorized network for accessing the PostgreSQL instance."
  #default     = "snapgreen-stg-subnet"
}

variable "authorized_network_value" {
  type        = string
  description = "The CIDR block for the authorized network."
  #default     = "********/16"
}

variable "network_id" {
  type        = string
  description = "The ID of the VPC network to use for the PostgreSQL instance."
  #default     = "projects/snapgreen-stg/global/networks/snapgreen-stg-vpc"
}

variable "tier" {
  description = "The machine tier for the Cloud SQL instance."
  type        = string
  default     = "db-custom-4-4096"  # Default to 4 vCPUs and 4 GB RAM
}

variable "disk_size" {
  description = "The initial storage size (in GB)."
  type        = number
  default     = 20
}

variable "disk_type" {
  description = "The disk type for the Cloud SQL instance (e.g., PD_HDD or PD_SSD)."
  type        = string
  default     = "PD_HDD"
}
variable "reporting_db_instance_name" {
  description = "The name of the Reporting PostgreSQL instance."
  type        = string
}

variable "reporting_db_name" {
  description = "The name of the database for the Reporting instance."
  type        = string
}

variable "reporting_db_user" {
  description = "The database user for the Reporting PostgreSQL instance."
  type        = string
}

variable "reporting_db_password" {
  description = "The password for the Reporting database user."
  type        = string
}

variable "database_flags" {
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "reporting_authorized_networks" {
  description = "List of authorized networks"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "maintenance_window" {
  description = "Day and hour for maintenance window"
  type = object({
    day  = number
    hour = number
  })
  default = {
    day  = 0
    hour = 0
  }
}

variable "main_authorized_networks" {
  type = list(object({
    name  = string
    value = string
  }))
}

variable "main_database_flags" {
  type = list(object({
    name  = string
    value = string
  }))
}

variable "reporting_maintenance_window" {
  description = "Maintenance window for reporting Cloud SQL instance"
  type = object({
    day  = number
    hour = number
  })
}

variable "main_maintenance_window" {
  description = "Maintenance window for main Cloud SQL instance"
  type = object({
    day  = number
    hour = number
  })
}