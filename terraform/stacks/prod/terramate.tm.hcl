stack {
  id          = "prod"
  description = "Production environment for SnapGreen"
}

generate_hcl "stack.tf" {
  content {
    terraform {
      backend "remote" {
        organization = "snapgreen"
        workspaces {
          name = "snapgreen-terraform-prod"
        }
      }
    }

    provider "google" {
      project = var.project_id
      region  = var.region
    }

    module "network_prod" {
      cluster_cidr  = var.cluster_cidr
      network_name  = var.network_name
      region        = var.region
      services_cidr = var.services_cidr
      source        = "../../modules/network"
      subnet_cidr   = var.subnet_cidr
      subnet_name   = var.subnet_name
    }

    module "artifact_registry" {
      source = "../../modules/registry"

      repositories = [
        {
          project_id    = var.project_id
          location      = "asia-southeast2"
          repository_id = "go"
          description   = "Go app repository"
          format        = "DOCKER"
        },
        {
          project_id    = var.project_id
          location      = "asia-southeast2"
          repository_id = "node"
          description   = "Node app artifact"
          format        = "DOCKER"
        }
      ]
    }

    module "ssh_firewall" {
      description = "Allow SSH traffic on port 22"
      name        = "allow-ssh"
      network     = var.network_name
      ports       = ["22"]
      protocol    = "tcp"
      source      = "../../modules/firewall"
      source_ranges = ["0.0.0.0/0"]
      target_tags  = ["allow-ssh"]
    }

    module "iap_ssh_firewall" {
      description = "Allow SSH via Cloud IAP"
      name        = "allow-iap-ssh-gke"
      network     = module.network_prod.vpc_id
      ports       = ["22"]
      direction   = "INGRESS"
      protocol    = "tcp"
      source      = "../../modules/firewall"
      source_ranges = ["************/20"]
      target_tags  = ["iap-access"]
    }

    module "gke_to_postgres_firewall" {
      source        = "../../modules/firewall"
      name          = "allow-gke-to-postgres"
      network       = module.network_prod.vpc_id
      protocol      = "tcp"
      ports         = ["5432"]
      source_ranges = ["********/16"]
      target_tags   = []  # Adjust if the PostgreSQL instance has specific tags, else leave empty
      description   = "Allow GKE nodes to access PostgreSQL"
    }

    module "egress_to_gke_api_firewall" {
      source            = "../../modules/firewall"
      name              = "allow-egress-to-gke-api"
      network           = module.network_prod.vpc_id
      protocol          = "tcp"
      ports             = ["443"]
      direction         = "EGRESS"
      destination_ranges = ["*********/28"]
      description       = "Allow egress traffic to the GKE API (private endpoint)"
    }

    module "cloud_nat_gw" {
      nat_name    = "nat-gw-prod"
      network     = module.network_prod.vpc_id
      region      = var.region
      router_name = "nat-router-prod"
      //static_ip   = null
      source      = "../../modules/cloud-nat"
    }

    module "gke_prod" {
      cluster_cidr          = var.cluster_cidr
      cluster_name          = var.cluster_name
      master_cidr           = var.master_cidr
      network_id            = module.network_prod.vpc_id
      node_machine_type     = var.node_machine_type
      region                = var.region
      services_cidr         = var.services_cidr
      source                = "../../modules/gke"
      subnet_id             = module.network_prod.subnet_id
      use_preemptible_nodes = var.use_preemptible_nodes
      node_count            = var.node_count
      min_node_count     = var.min_node_count
      max_node_count     = var.max_node_count
      # Pass the required variables
      master_ipv4_cidr_block = var.master_ipv4_cidr_block       # Unique range for production
      environment            = "snapgreen-prods"     # Environment-specific name
    }

    module "postgresql" {
      source           = "../../modules/postgresql"
      project_id       = var.project_id
      region           = var.region
      db_instance_name = "snapgreen-prods-db"
      db_name          = "snapgreen"
      db_user          = "snapgreen_prod_app"
      db_password      = var.db_password
      network_id        = "projects/snapgreen-prods/global/networks/snapgreen-prods-vpc"  # Update to match each environment
      authorized_network_name = var.subnet_name  # Set dynamically per environment
      authorized_network_value = var.subnet_cidr
      main_authorized_networks   = var.main_authorized_networks
      main_database_flags         = var.main_database_flags
      main_maintenance_window = var.main_maintenance_window
    }

  module "postgresql_reporting" {
  source           = "../../modules/postgresql-reporting"
  project_id       = var.project_id
  region           = var.region
  network_id       = var.network_id
  reporting_db_instance_name = var.reporting_db_instance_name
  reporting_db_name          = var.reporting_db_name
  reporting_db_user          = var.reporting_db_user
  reporting_db_password      = var.reporting_db_password
  
  reporting_authorized_networks = var.reporting_authorized_networks
  reporting_maintenance_window            = var.reporting_maintenance_window
}



    module "gcs" {
      buckets = {
        snapgreen-prod-backup = {
          lifecycle_age_days = 30
          location           = "asia-southeast2"
          storage_class      = "STANDARD"
        }
        snapgreen-prod-logs = {
          lifecycle_age_days = 90
          location           = "asia-southeast2"
          storage_class      = "NEARLINE"
        }
      }
      source = "../../modules/gcs"
    }
  }
}
