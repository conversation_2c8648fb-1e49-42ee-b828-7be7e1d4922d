stack {
  id          = "infra"
  description = "infra environment for SnapGreen"
}

generate_hcl "stack.tf" {
  content {
    terraform {
      backend "remote" {
        organization = "snapgreen"
        workspaces {
          name = "snapgreen-terraform-infra"
        }
      }
    }

    provider "google" {
      project = var.project_id
      region  = var.region
    }

    module "network_staging" {
      source       = "../../modules/network"
      network_name = var.network_name
      subnet_name  = var.subnet_name
      subnet_cidr  = var.subnet_cidr
      region       = var.region
      cluster_cidr  = var.cluster_cidr
      services_cidr = var.services_cidr
    }
    
    module "ssh_firewall" {
      source        = "../../modules/firewall"
      name          = "allow-ssh"
      network       = var.network_name
      protocol      = "tcp"
      ports         = ["22"]
      source_ranges = ["0.0.0.0/0"]
      target_tags   = ["allow-ssh"]
      description   = "Allow SSH traffic on port 22"
    }

    module "iap_ssh_firewall" {
      source        = "../../modules/firewall"
      description   = "Allow SSH via Cloud IAP"
      name          = "allow-iap-ssh-gke"
      network       = module.network_staging.vpc_id
      ports         = ["22"]
      protocol      = "tcp"
      source_ranges = ["************/20"]
      target_tags   = ["iap-access"]
    }

    module "cloud_nat_gw" {
      source      = "../../modules/cloud-nat"
      router_name = "nat-router-stg"
      nat_name    = "nat-gw-stg"
      network     = module.network_staging.vpc_id
      region      = var.region
    }
}
}