variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The GCP region for resources"
  type        = string
}

variable "network_name" {
  description = "The name of the VPC network"
  type        = string
}

variable "subnet_name" {
  description = "The name of the subnet"
  type        = string
}

variable "subnet_cidr" {
  description = "The CIDR block for the subnet"
  type        = string
}


variable "cluster_name" {
  description = "The name of the GKE cluster"
  type        = string
}

variable "node_count" {
  description = "The number of nodes in the GKE cluster"
  type        = number
}

variable "node_machine_type" {
  description = "The machine type for the GKE nodes"
  type        = string
}

variable "use_preemptible_nodes" {
  description = "Whether to use preemptible nodes"
  type        = bool
  default     = false
}

variable "master_cidr" {
  description = "CIDR block for master authorized networks"
  type        = string
}

variable "disk_size_gb" {
  description = "The size of the disk in GB for each node"
  type        = number
  default     = 50  # 100 GB disk
}

variable "cluster_cidr" {
  description = "CIDR block for the GKE cluster"
  type        = string
}

variable "services_cidr" {
  description = "CIDR block for GKE services"
  type        = string
}

variable "tier" {
  description = "The tier of the PostgreSQL instance (e.g., db-f1-micro)"
  type        = string
  default     = "db-f1-micro"  # Low spec for staging
}

variable "availability_type" {
  description = "The availability type for the PostgreSQL instance (ZONAL or REGIONAL)"
  type        = string
  default     = "ZONAL"
}

variable "ipv4_enabled" {
  description = "Enable public IP for the instance"
  type        = bool
  default     = false
}

# Minimum number of nodes for autoscaling
variable "min_node_count" {
  description = "The minimum number of nodes for autoscaling"
  type        = number
  default     = 3
}

# Maximum number of nodes for autoscaling
variable "max_node_count" {
  description = "The maximum number of nodes for autoscaling"
  type        = number
  default     = 5  # Adjust based on your scaling needs
}

variable "db_password" {
  type        = string
  description = "The password for the PostgreSQL database."
  sensitive   = true
}

variable "db_instance_name" {
  type        = string
  description = "The name of the PostgreSQL instance."
  default     = "snapgreen-stg-db"
}

variable "db_name" {
  type        = string
  description = "The name of the PostgreSQL database."
  default     = "snapgreen"
}

variable "db_user" {
  type        = string
  description = "The username for the PostgreSQL database."
  default     = "snapgreen_app"
}

variable "destination_ranges" {
  description = "CIDR ranges allowed by the rule (used for EGRESS)"
  type        = list(string)
  default     = []
}

variable "target_tags" {
  description = "Network tags that will apply the firewall rule"
  type        = list(string)
  default     = []
}

variable "description" {
  description = "Description of the firewall rule"
  type        = string
  default     = ""
}

variable "direction" {
  description = "Direction of the firewall rule, either INGRESS or EGRESS"
  type        = string
  default     = "INGRESS"
}

variable "source_ranges" {
  description = "CIDR ranges allowed by the rule (used for INGRESS)"
  type        = list(string)
  default     = []
}