// TERRAMATE: GENERATED AUTOMATICALLY DO NOT EDIT

terraform {
  backend "remote" {
    organization = "snapgreen"
    workspaces {
      name = "snapgreen-terraform-infra"
    }
  }
}
provider "google" {
  project = var.project_id
  region  = var.region
}
module "network_staging" {
  cluster_cidr  = var.cluster_cidr
  network_name  = var.network_name
  region        = var.region
  services_cidr = var.services_cidr
  source        = "../../modules/network"
  subnet_cidr   = var.subnet_cidr
  subnet_name   = var.subnet_name
}
module "ssh_firewall" {
  description = "Allow SSH traffic on port 22"
  name        = "allow-ssh"
  network     = var.network_name
  ports = [
    "22",
  ]
  protocol = "tcp"
  source   = "../../modules/firewall"
  source_ranges = [
    "0.0.0.0/0",
  ]
  target_tags = [
    "allow-ssh",
  ]
}
module "iap_ssh_firewall" {
  description = "Allow SSH via Cloud IAP"
  name        = "allow-iap-ssh-gke"
  network     = module.network_staging.vpc_id
  ports = [
    "22",
  ]
  protocol = "tcp"
  source   = "../../modules/firewall"
  source_ranges = [
    "************/20",
  ]
  target_tags = [
    "iap-access",
  ]
}
module "cloud_nat_gw" {
  nat_name    = "nat-gw-stg"
  network     = module.network_staging.vpc_id
  region      = var.region
  router_name = "nat-router-stg"
  source      = "../../modules/cloud-nat"
}
