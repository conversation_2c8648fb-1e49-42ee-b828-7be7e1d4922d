# Project and region
project_id     = "snapgreen-infra"
region         = "asia-southeast2"

# Network configuration
network_name   = "snapgreen-infra-vpc"
subnet_name    = "snapgreen-infra-subnet"
subnet_cidr    = "10.8.0.0/16"  # Non-overlapping CIDR block

# GKE cluster configuration (if applicable)
cluster_name          = "gke-infra-cluster"
node_count            = 3
min_node_count        = 2
max_node_count        = 3

node_machine_type     = "e2-medium"
disk_size_gb          = 20
use_preemptible_nodes = false
master_cidr           = "10.8.32.0/28"

# CIDR blocks for GKE
cluster_cidr  = "10.9.0.0/16"  # CIDR block for Kubernetes pods
services_cidr = "10.10.0.0/20" # CIDR block for Kubernetes services

# PostgreSQL Database Configuration (if needed)
db_instance_name = "snapgreen-infra-db"
db_name          = "snapgreen_infra"
db_user          = "snapgreen_infra_app"
# db_password export variable
# export TF_VAR_db_password="YourInfraDBPassword"

# Network Configuration
# network_id               = "projects/snapgreen-infra/global/networks/snapgreen-infra-vpc"
# authorized_network_name  = "snapgreen-infra-subnet"
# authorized_network_value = "10.8.0.0/16"
