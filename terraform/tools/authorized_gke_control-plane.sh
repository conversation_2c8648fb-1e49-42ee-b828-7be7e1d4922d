#!/bin/bash

# Set variables
CLUSTER_NAME="gke-stg-cluster"
REGION="asia-southeast2"
PROJECT_ID="snapgreen-stg"

# Try to get the public IP using ifconfig.me
PUBLIC_IP=$(curl -s ifconfig.me)

# If the first attempt fails, try another service to get the public IP
if [[ -z "$PUBLIC_IP" ]]; then
  PUBLIC_IP=$(curl -s https://api.ipify.org)
fi

# If both attempts fail, exit with an error
if [[ -z "$PUBLIC_IP" ]]; then
  echo "Failed to retrieve public IP. Exiting..."
  exit 1
fi

# Existing authorized networks (excluding the IP if already exists)
EXISTING_NETWORKS=$(gcloud container clusters describe $CLUSTER_NAME \
  --region $REGION \
  --project $PROJECT_ID \
  --format="get(masterAuthorizedNetworksConfig.cidrBlocks[].cidrBlock)" | tr '\n' ',' | sed 's/,$//')

# Combine existing networks with the new IP (retain current IPs)
if [[ -n "$EXISTING_NETWORKS" ]]; then
  AUTHORIZED_NETWORKS="${EXISTING_NETWORKS},${PUBLIC_IP}/32"
else
  AUTHORIZED_NETWORKS="${PUBLIC_IP}/32"
fi

# Update GKE cluster authorized networks
gcloud container clusters update $CLUSTER_NAME \
  --region $REGION \
