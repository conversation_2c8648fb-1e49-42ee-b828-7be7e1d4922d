#!/bin/bash

# Variables
PROJECT_ID="snapgreen-stg"
DNS_ZONE_NAME="snapgreen"
DOMAIN="snapgreen.world"
EXTERNAL_DNS_SA_NAME="external-dns"
SERVICE_ACCOUNT_EMAIL="$EXTERNAL_DNS_SA_NAME@$PROJECT_ID.iam.gserviceaccount.com"
KEY_FILE="external-dns-key.json"
NAMESPACE="external-dns"

# 1. Create a Kubernetes namespace for ExternalDNS if it doesn't exist
echo "Checking if namespace $NAMESPACE exists..."
if kubectl get namespace $NAMESPACE &>/dev/null; then
    echo "Namespace $NAMESPACE already exists. Skipping creation."
else
    echo "Creating Kubernetes namespace: $NAMESPACE"
    kubectl create namespace $NAMESPACE
fi

# 2. Set up Google Cloud DNS Zone if it doesn't exist
echo "Checking if Cloud DNS managed zone $DNS_ZONE_NAME exists..."
if gcloud dns managed-zones describe $DNS_ZONE_NAME --project=$PROJECT_ID &>/dev/null; then
    echo "Cloud DNS managed zone $DNS_ZONE_NAME already exists. Skipping creation."
else
    echo "Creating Cloud DNS managed zone: $DNS_ZONE_NAME"
    gcloud dns managed-zones create $DNS_ZONE_NAME \
        --description="Managed zone for $DOMAIN" \
        --dns-name="$DOMAIN" \
        --project=$PROJECT_ID
fi

# 3. Create the service account for ExternalDNS if it doesn't exist
echo "Checking if Service Account $EXTERNAL_DNS_SA_NAME exists..."
if gcloud iam service-accounts describe $SERVICE_ACCOUNT_EMAIL &>/dev/null; then
    echo "Service Account $EXTERNAL_DNS_SA_NAME already exists. Skipping creation."
else
    echo "Creating Service Account: $EXTERNAL_DNS_SA_NAME"
    gcloud iam service-accounts create $EXTERNAL_DNS_SA_NAME \
        --description="Service Account for ExternalDNS" \
        --display-name="ExternalDNS Service Account"
fi

# 4. Grant DNS Admin role to the Service Account if not already granted
echo "Checking if DNS Admin role is already granted..."
EXISTING_ROLE_BINDING=$(gcloud projects get-iam-policy $PROJECT_ID \
    --flatten="bindings[].members" \
    --format="table(bindings.members)" \
    --filter="bindings.members:serviceAccount:$SERVICE_ACCOUNT_EMAIL AND bindings.role:roles/dns.admin")
if [[ -n "$EXISTING_ROLE_BINDING" ]]; then
    echo "DNS Admin role already granted to the Service Account. Skipping role assignment."
else
    echo "Granting DNS Admin role to the Service Account"
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
        --role="roles/dns.admin"
fi

# 5. Generate a key for the Service Account if it doesn't exist
if [[ -f "$KEY_FILE" ]]; then
    echo "Key file $KEY_FILE already exists. Skipping key generation."
else
    echo "Generating key for the Service Account"
    gcloud iam service-accounts keys create $KEY_FILE \
        --iam-account=$SERVICE_ACCOUNT_EMAIL
fi

# 6. Create Kubernetes Secret for the Service Account key if it doesn't exist
echo "Checking if Kubernetes Secret external-dns-key exists..."
if kubectl get secret external-dns-key --namespace=$NAMESPACE &>/dev/null; then
    echo "Kubernetes Secret external-dns-key already exists. Skipping secret creation."
else
    echo "Creating Kubernetes Secret for ExternalDNS key"
    kubectl create secret generic external-dns-key \
        --from-file=$KEY_FILE \
        --namespace=$NAMESPACE
fi

# 7. Install or upgrade ExternalDNS using Helm
echo "Installing or upgrading ExternalDNS with Helm"
helm repo add bitnami https://charts.bitnami.com/bitnami
helm upgrade --install external-dns bitnami/external-dns \
    --set provider=google \
    --set google.project=$PROJECT_ID \
    --set google.serviceAccountSecret=external-dns-key \
    --set google.serviceAccountSecretKey=credentials.json \
    --set domainFilters={$DOMAIN} \
    --set txtOwnerId=external-dns-identifier \
    --namespace $NAMESPACE

# 8. Display completion message
echo "ExternalDNS setup complete in the namespace $NAMESPACE. Ensure you configure your ingress with the appropriate annotations to automatically manage DNS records."
