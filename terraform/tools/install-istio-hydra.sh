#!/bin/bash

# Variables
PROJECT_ID="snapgreen-stg"
NAMESPACE_ISTIO="istio-system"
NAMESPACE_HYDRA="hydra"
DOMAIN="hydra.snapgreen.world"
SECRET="Sn@pGrenn2024" # Replace this with a secure secret

# 1. Install Istio
echo "Installing Istio..."
curl -L https://istio.io/downloadIstio | sh -
cd istio-*
export PATH=$PWD/bin:$PATH

# Install Istio with the default profile
istioctl install --set profile=minimal -y

# Enable sidecar injection for default namespace
kubectl label namespace snapgreen-apps istio-injection=enabled

# Verify Istio installation
kubectl get pods -n $NAMESPACE_ISTIO

# 2. Install Hydra
echo "Creating namespace for Hydra..."
kubectl create namespace $NAMESPACE_HYDRA

echo "Installing Hydra using Helm..."
helm repo add ory https://k8s.ory.sh/helm/charts
helm repo update

helm install hydra ory/hydra -n $NAMESPACE_HYDRA \
  --set postgresql.enabled=true \
  --set secrets.system=$SECRET \
  --set hydra.autoMigrate=true

# Verify Hydra installation
kubectl get pods -n $NAMESPACE_HYDRA

# 3. Create Istio Gateway and VirtualService for Hydra
echo "Creating Istio Gateway and VirtualService for $DOMAIN..."

# Create gateway YAML
cat <<EOF > hydra-gateway.yaml
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: hydra-gateway
  namespace: $NAMESPACE_HYDRA
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "$DOMAIN"
EOF

# Apply gateway YAML
kubectl apply -f hydra-gateway.yaml

# Create VirtualService YAML
cat <<EOF > hydra-virtualservice.yaml
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: hydra
  namespace: $NAMESPACE_HYDRA
spec:
  hosts:
  - "$DOMAIN"
  gateways:
  - hydra-gateway
  http:
  - match:
    - uri:
        prefix: "/"
    route:
    - destination:
        host: hydra-public
        port:
          number: 80
  - match:
    - uri:
        prefix: "/admin"
    route:
    - destination:
        host: hydra-admin
        port:
          number: 80
EOF

# Apply VirtualService YAML
kubectl apply -f hydra-virtualservice.yaml

# 4. Get Istio Ingress IP and update DNS
echo "Getting Istio Ingress Gateway external IP..."
INGRESS_IP=$(kubectl get svc istio-ingressgateway -n $NAMESPACE_ISTIO -o jsonpath='{.status.loadBalancer.ingress[0].ip}')

if [ -z "$INGRESS_IP" ]; then
  echo "ERROR: Unable to retrieve the external IP of Istio Ingress Gateway. Make sure LoadBalancer is provisioned."
else
  echo "External IP of Istio Ingress Gateway is: $INGRESS_IP"
  echo "Please update your DNS to point $DOMAIN to $INGRESS_IP."
fi

echo "Installation complete. Verify DNS settings and access Hydra at http://$DOMAIN and http://$DOMAIN/admin."
