#!/bin/bash

# Variables
PROJECT_ID="snapgreen-stg"          # Your Google Cloud project ID
CLUSTER_NAME="gke-stg-cluster"      # Your GKE cluster name
REGION="asia-southeast2"            # Your GKE cluster region
NODE_POOL_NAME="custom4gb-pool"  # Your GKE node pool name
NUM_NODES=3                         # The number of nodes you want to scale up to

# Slack webhook URL for notifications
SLACK_WEBHOOK_URL="*******************************************************************************"  # Replace with your Slack webhook URL

# Log file setup
BASE_LOG_DIR="logs"  # Logs directory relative to the script's location
MONTHLY_LOG_DIR="${BASE_LOG_DIR}/$(date +'%Y-%m')"  # Monthly folder, e.g., "2024-09"
LOG_FILE="${MONTHLY_LOG_DIR}/gke_shutdown_$(date +'%Y-%m-%d').log"  # Daily log file, e.g., "2024-09-05.log"


# Logout any existing gcloud authentication
#log_message "Logging out of any active gcloud authentication..."
gcloud auth revoke --all 2>&1 | tee -a "$LOG_FILE"


# Service account key file path
SERVICE_ACCOUNT_KEY_FILE="/app/cron/sa-utility-vm.json"  # Update this path




# Create the monthly log directory if it doesn't exist
mkdir -p "$MONTHLY_LOG_DIR"

# Function to log messages
log_message() {
    echo "$(date +'%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to send Slack notifications
send_slack_notification() {
    curl -X POST -H 'Content-type: application/json' --data "{\"text\":\"$1\"}" $SLACK_WEBHOOK_URL
}

# Start logging
log_message "Shutdown script initiated."

# Authenticate with Google Cloud (if not already authenticated)
# gcloud auth activate-service-account --key-file=/path/to/your-service-account-key.json


# Authenticate with Google Cloud
log_message "Authenticating with service account..."
gcloud auth activate-service-account --key-file="$SERVICE_ACCOUNT_KEY_FILE" 2>&1 | tee -a "$LOG_FILE"


# Set the project
gcloud config set project "$PROJECT_ID" 2>&1 | tee -a "$LOG_FILE"

# Disable auto-upgrade and auto-repair for the node pool
log_message "Disabling auto-upgrade and auto-repair for node pool $NODE_POOL_NAME in cluster $CLUSTER_NAME..."
gcloud container node-pools update "$NODE_POOL_NAME" \
  --cluster "$CLUSTER_NAME" \
  --region "$REGION" \
  --no-enable-autoupgrade \
  --no-enable-autorepair \
  2>&1 | tee -a "$LOG_FILE"
log_message "Auto-upgrade and auto-repair disabled."

# Disable autoscaling for the node pool
log_message "Disabling autoscaling for node pool $NODE_POOL_NAME in cluster $CLUSTER_NAME..."
gcloud container node-pools update "$NODE_POOL_NAME" \
  --cluster "$CLUSTER_NAME" \
  --region "$REGION" \
  --no-enable-autoscaling \
  --min-nodes=0 \
  --max-nodes=0 \
  2>&1 | tee -a "$LOG_FILE"
log_message "Autoscaling disabled."

# Scale down the node pool to zero nodes
log_message "Scaling down node pool $NODE_POOL_NAME to zero nodes..."
gcloud container clusters resize "$CLUSTER_NAME" \
  --node-pool "$NODE_POOL_NAME" \
  --num-nodes 0 \
  --region "$REGION" \
  --quiet \
  2>&1 | tee -a "$LOG_FILE"
log_message "Node pool $NODE_POOL_NAME scaled down to zero nodes."

# Shutdown SQL Cloud Staging
log_message "Shutdown dc-id SQL Instance"
gcloud sql instances patch snapgreen-stg-db --activation-policy=NEVER

# End logging
log_message "Shutdown script completed."

# Send Slack notification
send_slack_notification "[$PROJECT_ID] GKE shutdown script completed successfully for cluster $CLUSTER_NAME and node pool $NODE_POOL_NAME."