#!/bin/bash

# Variables
PROJECT_ID="snapgreen-stg"  # Your Google Cloud project ID
CLUSTER_NAME="gke-stg-cluster"             # Your GKE cluster name
REGION="asia-southeast2"            # Your GKE cluster region
NODE_POOL_NAME="snapgreen-stg-gke"      # Your GKE node pool name
NUM_NODES=3                         # The number of nodes you want to scale up to

# Slack webhook URL for notifications
SLACK_WEBHOOK_URL="*******************************************************************************"  # Replace with your Slack webhook URL

# Log file setup
BASE_LOG_DIR="logs"  # Logs directory relative to the script's location
MONTHLY_LOG_DIR="${BASE_LOG_DIR}/$(date +'%Y-%m')"  # Monthly folder, e.g., "2024-09"
LOG_FILE="${MONTHLY_LOG_DIR}/gke_start_$(date +'%Y-%m-%d').log"  # Daily log file, e.g., "2024-09-05.log"

# Create the monthly log directory if it doesn't exist
mkdir -p "$MONTHLY_LOG_DIR"

# Function to log messages
log_message() {
    echo "$(date +'%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to send Slack notifications
send_slack_notification() {
    curl -X POST -H 'Content-type: application/json' --data "{\"text\":\"$1\"}" $SLACK_WEBHOOK_URL
}

# Start logging
log_message "Start script initiated."

# Authenticate with Google Cloud (if not already authenticated)
# gcloud auth activate-service-account --key-file=/path/to/your-service-account-key.json

# Set the project
gcloud config set project "$PROJECT_ID"

# Scale up the node pool to the desired number of nodes
log_message "Scaling up node pool $NODE_POOL_NAME to $NUM_NODES nodes in cluster $CLUSTER_NAME..."
gcloud container clusters resize "$CLUSTER_NAME" \
  --node-pool "$NODE_POOL_NAME" \
  --num-nodes "$NUM_NODES" \
  --region "$REGION" \
  --quiet \
  2>&1 | tee -a "$LOG_FILE"
log_message "Node pool $NODE_POOL_NAME scaled up to $NUM_NODES nodes."

# Re-enable auto-upgrade and auto-repair
log_message "Enabling auto-upgrade and auto-repair for node pool $NODE_POOL_NAME in cluster $CLUSTER_NAME..."
gcloud container node-pools update "$NODE_POOL_NAME" \
  --cluster "$CLUSTER_NAME" \
  --region "$REGION" \
  --enable-autoupgrade \
  --enable-autorepair \
  2>&1 | tee -a "$LOG_FILE"
log_message "Auto-upgrade and auto-repair enabled."

# Re-enable autoscaling for the node pool
log_message "Enabling autoscaling for node pool $NODE_POOL_NAME in cluster $CLUSTER_NAME..."
gcloud container node-pools update "$NODE_POOL_NAME" \
  --cluster "$CLUSTER_NAME" \
  --region "$REGION" \
  --enable-autoscaling --min-nodes 1 --max-nodes 3 \
  2>&1 | tee -a "$LOG_FILE"
log_message "Autoscaling enabled with min nodes 1 and max nodes 3."

# End logging
log_message "Start script completed."

# Send Slack notification
send_slack_notification "GKE VM staging start script completed successfully for cluster $CLUSTER_NAME and node pool $NODE_POOL_NAME."
