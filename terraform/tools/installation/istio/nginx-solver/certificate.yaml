apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: snapgreen-cert-tls
  namespace: istio-system
spec:
  secretName: snapgreen-cert-tls  # Secret where the certificate will be stored
  issuerRef:
    name: letsencrypt-production-snapgreen  # Reference the updated ClusterIssuer
    kind: ClusterIssuer
  dnsNames:
  - "*.snapgreen.world"  # Wildcard for all subdomains
  - "snapgreen.world"    # Root domain
