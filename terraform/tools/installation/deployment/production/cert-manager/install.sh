#!/bin/bash
helm upgrade --install cert-manager jetstack/cert-manager \
    --namespace cert-manager \
    --create-namespace \
    --set installCRDs=true \
     --set extraArgs="{--cluster-issuer-ambient-credentials,--dns01-recursive-nameservers-only,--dns01-recursive-nameservers=kube-dns.kube-system.svc.cluster.local:53}"



     Endpoints:         10.6.3.4:53,10.6.6.6:53
Port:              dns-tcp  53/TCP
TargetPort:        53/TCP
Endpoints:         10.6.3.4:53,10.6.6.6:53