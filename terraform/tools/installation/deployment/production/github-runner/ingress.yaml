apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: github-actions-runner-ingress
  namespace: actions-runner-system
  annotations:
    kubernetes.io/ingress.class: "nginx"
spec:
  rules:
  - http:
      paths:
      - path: /actions-runner-controller-github-webhook-server
        pathType: Prefix
        backend:
          service:
            name: actions-runner-controller-github-webhook-server
            port:
              number: 80
