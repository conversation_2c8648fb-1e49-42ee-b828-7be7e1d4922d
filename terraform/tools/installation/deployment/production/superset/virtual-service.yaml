apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: superset-vs
  namespace: superset
spec:
  hosts:
    - superset-apps.snapgreen.world
  gateways:
    - snapgreen-marvel-app/marvel-gateway-snapgreen-world  # Update to correct namespace
  http:
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: superset
            port:
              number: 8088



# helm upgrade --install superset superset/superset \
#   --namespace superset \
#   --create-namespace \
#   -f values.yml