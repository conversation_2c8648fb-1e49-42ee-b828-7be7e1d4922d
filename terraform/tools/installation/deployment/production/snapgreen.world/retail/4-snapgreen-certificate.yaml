apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: snapgreen-domain-server
  namespace: istio-system
spec:
  secretName: wild-card-tls-prod
  commonName: "*.snapgreen.world"
  isCA: false
  usages:
    - server auth
    - client auth
  duration: 2160h # 90 days, Let's Encrypt maximum duration
  renewBefore: 360h # 15 days before expiration
  dnsNames:
  - "*.snapgreen.world"
  issuerRef:
    name:  letsencrypt-prod-snap
    kind: ClusterIssuer