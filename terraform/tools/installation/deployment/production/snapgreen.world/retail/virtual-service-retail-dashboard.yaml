apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: retail-dashboard-vs
  namespace: snapgreen-retail-app
spec:
  hosts:
  - "backoffice.snapgreen.world"  # Specific subdomain
  gateways:
  - snapgreen-retail-app/retail-gateway  # Update to correct namespace
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: retail-dashboard-svc.snapgreen-retail-app.svc.cluster.local
        port:
          number: 3000        
    corsPolicy:
      maxAge: 1m
      allowCredentials: true
      allowHeaders:
      - "*"
      allowMethods:
      - GET
      - POST
      - OPTIONS
      allowOrigins:
      - exact: "*"         