apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod-snap
  namespace: cert-manager
spec:
  acme:
    email: luk<PERSON>@snapgreen.world # Replace with your email
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: letsencrypt-prod-private-key
    solvers:
    - dns01:
        cloudDNS:
          project: snapgreen-stg # Replace with your GCP project ID
          hostedZoneName: snapgreen  # Replace with your DNS zone name
          serviceAccountSecretRef:
            name: clouddns-secret
            key: clouddns-key.json


# kubectl create secret generic clouddns-secret \
#   --from-file=clouddns-key.json=external-dns-key.json \
#   -n cert-manager


#kubectl create secret generic clouddns-secret \
 # --from-file=clouddns-key.json=/path/to/your-service-account-key.json \
  #-n cert-manager