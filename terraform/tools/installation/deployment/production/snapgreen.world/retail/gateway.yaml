apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: retail-gateway
  namespace: snapgreen-retail-app
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "*"
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: wild-card-tls-prod
    hosts:
    - "*"