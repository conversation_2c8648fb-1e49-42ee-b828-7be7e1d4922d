apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: marvel-gateway-snapgreen-world
  namespace: snapgreen-marvel-app
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "marvel-post-order.snapgreen.world"
    - "marvel-pre-order.snapgreen.world"
    - "marvel-dashboard.snapgreen.world"
    - "kratos-api.snapgreen.world"
    - "keto-api.snapgreen.world"
    - "hydra.snapgreen.world"
    - "superset-apps.snapgreen.world"
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: wild-card-tls-prod
    hosts:
    - "marvel-post-order.snapgreen.world"
    - "marvel-pre-order.snapgreen.world"
    - "marvel-dashboard.snapgreen.world"
    - "kratos-api.snapgreen.world"
    - "keto-api.snapgreen.world"
    - "hydra.snapgreen.world"
    - "superset-apps.snapgreen.world"
  - port:
      number: 8081
      name: grpc
      protocol: HTTP2
    hosts:
    - "marvel-post-order.snapgreen.world"
    - "marvel-pre-order.snapgreen.world"
    - "marvel-dashboard.snapgreen.world"
    - "kratos-api.snapgreen.world"
