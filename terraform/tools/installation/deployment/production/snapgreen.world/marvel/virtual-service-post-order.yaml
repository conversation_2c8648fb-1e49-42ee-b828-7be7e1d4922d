apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: marvel-post-vs
  namespace: snapgreen-marvel-app
spec:
  hosts:
  - "marvel-post-order.snapgreen.world"  # Specific subdomain
  gateways:
  - snapgreen-marvel-app/marvel-gateway-snapgreen-world  # Update to correct namespace
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: post-order-svc.snapgreen-marvel-app.svc.cluster.local
        port:
          number: 8080
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: post-order-svc.snapgreen-marvel-app.svc.cluster.local
        port:
          number: 8081        
