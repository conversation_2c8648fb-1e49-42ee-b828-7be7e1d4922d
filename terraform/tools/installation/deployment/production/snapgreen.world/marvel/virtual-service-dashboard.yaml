apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: marvel-dashboard-vs
  namespace: snapgreen-marvel-app
spec:
  hosts:
  - "marvel-dashboard.snapgreen.world"  # Specific subdomain
  gateways:
  - snapgreen-marvel-app/marvel-gateway-snapgreen-world  # Update to correct namespace
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: marvel-dashboard-svc.snapgreen-marvel-app.svc.cluster.local
        port:
          number: 3000
