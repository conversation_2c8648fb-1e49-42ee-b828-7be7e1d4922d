apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: marvel-pre-vs
  namespace: snapgreen-marvel-app
spec:
  hosts:
  - "marvel-pre-order.snapgreen.world"  
  gateways:
  - snapgreen-marvel-app/marvel-gateway-snapgreen-world  
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: pre-order-svc.snapgreen-marvel-app.svc.cluster.local
        port:
          number: 8080
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: pre-order-svc.snapgreen-marvel-app.svc.cluster.local
        port:
          number: 8081