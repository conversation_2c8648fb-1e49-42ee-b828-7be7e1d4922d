apiVersion: apps/v1
kind: Deployment
metadata:
  name: kratos
  namespace: kratos
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kratos
  template:
    metadata:
      labels:
        app: kratos
    spec:
      containers:
      - name: kratos
        image: oryd/kratos:v1.3.0
        command: ["kratos", "serve", "--config", "/etc/config/kratos.yaml"]
        env:
        - name: DSN
          valueFrom:
            secretKeyRef:
              name: kratos-secrets
              key: DSN
        - name: CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: kratos-secrets
              key: CLIENT_ID
        - name: CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: kratos-secrets
              key: CLIENT_SECRET
        - name: COOKIE_SECRET
          valueFrom:
            secretKeyRef:
              name: kratos-secrets
              key: COOKIE_SECRET
        volumeMounts:
        - name: config-volume
          mountPath: /etc/config/kratos.yaml
          subPath: kratos.yaml
        - name: schema-volume
          mountPath: /etc/config/identity.schema.json
          subPath: identity.schema.json
        - name: jsonnet-volume
          mountPath: /etc/config/oidc.google.jsonnet
          subPath: oidc.google.jsonnet
        - name: jsonnet-volume
          mountPath: /etc/config/assign-role-body.jsonnet
          subPath: assign-role-body.jsonnet
      volumes:
      - name: config-volume
        configMap:
          name: kratos-config
      - name: schema-volume
        configMap:
          name: kratos-identity-schema
      - name: jsonnet-volume
        configMap:
          name: kratos-jsonnet-config
---
apiVersion: v1
kind: Service
metadata:
  name: kratos-public
  namespace: kratos
spec:
  selector:
    app: kratos
  ports:
    - protocol: TCP
      port: 80
      targetPort: 4433
