    dsn: *************************************************/kratos?sslmode=disable
    serve:
      public:
        base_url: https://api-kratos.snapgreen.world
        cors:
          enabled: true
          allowed_origins:
            - https://marvel-dashboard.snapgreen.world
          allowed_methods:
            - POST
            - GET
            - PUT
            - PATCH
            - DELETE
          allowed_headers:
            - Authorization
            - Content-Type
          exposed_headers:
            - Content-Type
          allow_credentials: true

    selfservice:
      default_browser_return_url: https://marvel-dashboard.snapgreen.world
      allowed_return_urls:
        - https://marvel-dashboard.snapgreen.world
        - http://localhost:4455
        - http://localhost:19006/Callback
        - exp://localhost:8081/--/Callback

      methods:
        password:
          enabled: true
        totp:
          config:
            issuer: Kratos
          enabled: true
        lookup_secret:
          enabled: true
        link:
          enabled: true
        code:
          enabled: true
        oidc:
          enabled: true
          config:
            providers:
              - id: google
                provider: google
                client_id: CHANGE_ME_CLIENT_ID
                client_secret: CHANGE_ME_CLIENT_SECRET
                issuer_url: https://accounts.google.com
                mapper_url: file:///etc/config/oidc.google.jsonnet
                scope:
                  - email
                  - profile

      flows:
        login:
          ui_url: https://marvel-dashboard.snapgreen.world/login
        registration:
          ui_url: https://marvel-dashboard.snapgreen.world/registration
          after:
            oidc:
              hooks:
                - hook: session
                - hook: web_hook
                  config:
                    url: http://pre-order-svc:8080/api/v1/user/assign-role
                    method: POST
                    body: file:///etc/config/assign-role-body.jsonnet
                    response:
                      ignore: true
                      parse: false
                    headers:
                      name: Content-Type
                      value: application/json
                      X-Internal-Code: "CHANGE_ME_INTERNAL_CODE"
                      X-Internal-Request: "true"
        settings:
          ui_url: https://marvel-dashboard.snapgreen.world/settings
        recovery:
          ui_url: https://marvel-dashboard.snapgreen.world/recovery
        verification:
          ui_url: https://marvel-dashboard.snapgreen.world/verification
        error:
          ui_url: https://marvel-dashboard.snapgreen.world/error

    log:
      level: debug
      format: text
      leak_sensitive_values: true

    secrets:
      cookie:
        - PLEASE-CHANGE-ME-I-AM-VERY-INSECURE
      cipher:
        - 32-LONG-SECRET-NOT-SECURE-AT-ALL

    ciphers:
      algorithm: xchacha20-poly1305

    hashers:
      algorithm: bcrypt
      bcrypt:
        cost: 8

    identity:
      default_schema_id: default
      schemas:
        - id: default
          url: file:///etc/config/identity.schema.json

    courier:
      smtp:
        connection_uri: smtps://test:test@mailslurper:1025/?skip_ssl_verify=true

    feature_flags:
      use_continue_with_transitions: true
