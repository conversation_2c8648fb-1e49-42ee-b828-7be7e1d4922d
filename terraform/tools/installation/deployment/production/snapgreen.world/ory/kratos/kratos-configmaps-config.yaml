apiVersion: v1
kind: ConfigMap
metadata:
  name: kratos-config
  namespace: kratos
data:
  kratos.yaml: |
    version: v0.13.0

    dsn: ${DSN}

    serve:
      public:
        base_url: https://kratos-api.snapgreen.world
        cors:
          enabled: true
          allowed_origins:
            - https://*.snapgreen.world
            - http://*.snapgreen.world
          allowed_methods:
            - POST
            - GET
            - PUT
            - PATCH
            - DELETE
          allowed_headers:
            - Authorization
            - Content-Type
            - Access-Control-Allow-Credentials
            - Access-Control-Allow-Origin
            - Access-Control-Expose-Headers
            - Cookie
          

          exposed_headers:
            - Content-Type
            - Set-Cookie
          allow_credentials: true

    selfservice:
      default_browser_return_url: https://marvel-dashboard.snapgreen.world
      allowed_return_urls:
        - https://marvel-dashboard.snapgreen.world/dashboard
        - http://marvel-dashboard.snapgreen.world/dashboard
        - http://localhost:4455
        - http://localhost:19006/Callback
        - exp://localhost:8081/--/Callback

      methods:
        password:
          enabled: true
        totp:
          enabled: true
          config:
            issuer: <PERSON><PERSON><PERSON>
        lookup_secret:
          enabled: true
        link:
          enabled: true
        code:
          enabled: true
        oidc:
          enabled: true
          config:
            providers:
              - id: google
                provider: google
                client_id: ************-ags3agv6eim80235oa4cels7k9do5h4h.apps.googleusercontent.com
                client_secret: 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
                issuer_url: https://accounts.google.com
                mapper_url: file:///etc/config/oidc.google.jsonnet
                scope:
                  - email
                  - profile

      flows:
        login:
          ui_url: https://marvel-dashboard.snapgreen.world/login
        registration:
          ui_url: https://marvel-dashboard.snapgreen.world/registration
          after:
            oidc:
              hooks:
                - hook: session
                - hook: web_hook
                  config:
                    url: http://pre-order-svc.snapgreen-marvel-app.svc.cluster.local:8080/api/v1/user/assign-role
                    method: POST
                    body: file:///etc/config/assign-role-body.jsonnet
                    response:
                      ignore: true
                      parse: false
                    headers:
                      name: Content-Type
                      value: application/json
                      X-Internal-Code: "01JBEZX0BYD4SG14MYWVZ27ZJ1"
                      X-Internal-Request: "true"

        settings:
          ui_url: https://marvel-dashboard.snapgreen.world/settings
        recovery:
          ui_url: https://marvel-dashboard.snapgreen.world/recovery
        verification:
          ui_url: https://marvel-dashboard.snapgreen.world/verification
        error:
          ui_url: https://marvel-dashboard.snapgreen.world/error

    log:
      level: debug
      format: text
      leak_sensitive_values: true

    secrets:
      cookie:
        - ${COOKIE_SECRET}
      cipher:
        - kDbZYaHDWfWWjmXjKCsucYRRotfXKCMA

    ciphers:
      algorithm: xchacha20-poly1305

    hashers:
      algorithm: bcrypt
      bcrypt:
        cost: 8

    identity:
      default_schema_id: default
      schemas:
        - id: default
          url: file:///etc/config/identity.schema.json

    cookies:
      domain: .snapgreen.world
      path: /                   
      same_site: Lax      
          
    courier:
      smtp:
        connection_uri: smtps://test:test@mailslurper:1025/?skip_ssl_verify=true