apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: keto-virtual-service
  namespace: keto 
spec:
  hosts:
  - "keto-api.snapgreen.world"
  gateways:
  - snapgreen-marvel-app/marvel-gateway-snapgreen-world
  http:
  # - match:
  #   - uri:
  #       prefix: "/read"     
  #   route:
  #   - destination:
  #       host: keto-read.keto.svc.cluster.local      
  #       port:
  #         number: 80
  - match:
    - uri:
        prefix: "/write"     
    route:
    - destination:
        host: keto-write.keto.svc.cluster.local    
        port:
          number: 80