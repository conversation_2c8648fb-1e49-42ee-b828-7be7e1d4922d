apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: kratos-virtual-service
  namespace: kratos
spec:
  hosts:
    - "kratos-api.snapgreen.world"
  gateways:
    - snapgreen-marvel-app/marvel-gateway-snapgreen-world
  http:
    - match:
        - uri:
            prefix: /
      # headers:
      #   response:     
      #     add: 
      #       access-control-allow-origin: https://marvel-dashboard.snapgreen.world
      #       access-control-allow-credentials: "true"
      #       access-control-expose-headers: Content-Type
      route:
        - destination:
            host: kratos-public.kratos.svc.cluster.local
            port:
              number: 80
    