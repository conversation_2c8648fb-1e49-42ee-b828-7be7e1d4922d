# Kafka on K8s

Resources for a [tutorial](https://rafaelnatali.wixsite.com/rmn-technology/blog/categories/kafka) that covers running [Kafka v3.5.x](https://docs.confluent.io/platform/current/installation/versions-interoperability.html) using the consensus protocol [Apache Kafka Raft (KRaft)](https://developer.confluent.io/learn/kraft/) on a Minikube-based Kubernetes cluster.

- [Quickstart: confluent-local](./confluent-local/README.md)
- [Kafka with SSL](./ssl/README.md)
- [Kafka with SASL Authentication](./sasl/README.md)
- [Kafka with SASL and ACLs](./acls/README.md)

## Contributing

Feel free to contribute by opening issues or pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.
