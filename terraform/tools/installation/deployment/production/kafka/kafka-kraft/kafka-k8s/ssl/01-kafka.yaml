---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kafka
  namespace: kafka
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: kafka
  name: kafka-headless
  namespace: kafka
spec:
  clusterIP: None
  clusterIPs:
  - None
  internalTrafficPolicy: Cluster
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - name: tcp-kafka-int
    port: 9092
    protocol: TCP
    targetPort: tcp-kafka-int
  - name: tcp-kafka-ssl
    port: 9093
    protocol: TCP
    targetPort: tcp-kafka-ssl
  selector:
    app: kafka
  sessionAffinity: None
  type: ClusterIP
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: kafka
  name: kafka
  namespace: kafka
spec:
  podManagementPolicy: Parallel
  replicas: 3
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: kafka
  serviceName: kafka-headless
  template:
    metadata:
      labels:
        app: kafka
    spec:
      serviceAccountName: kafka
      containers:
      - command:
        - sh
        - -exc
        - |
          export KAFKA_NODE_ID=${HOSTNAME##*-} && \
          export KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://${POD_NAME}.kafka-headless.kafka.svc.cluster.local:9092,SSL://${POD_NAME}.kafka-headless.kafka.svc.cluster.local:9093
          export KAFKA_SSL_TRUSTSTORE_FILENAME=${POD_NAME}.server.truststore.jks
          export KAFKA_SSL_KEYSTORE_FILENAME=${POD_NAME}.server.keystore.jks
          export KAFKA_OPTS="-Djavax.net.debug=all"

          exec /etc/confluent/docker/run
        env:
        - name: KAFKA_SSL_KEY_CREDENTIALS
          value: "broker_creds"
        - name: KAFKA_SSL_KEYSTORE_CREDENTIALS
          value: "broker_creds"
        - name: KAFKA_SSL_TRUSTSTORE_CREDENTIALS
          value: "broker_creds"
        - name: KAFKA_LISTENER_SECURITY_PROTOCOL_MAP
          value: "CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,SSL:SSL"
        - name: CLUSTER_ID
          value: "6PMpHYL9QkeyXRj9Nrp4KA"
        - name: KAFKA_CONTROLLER_QUORUM_VOTERS
          value: "<EMAIL>:29093,<EMAIL>:29093,<EMAIL>:29093"
        - name: KAFKA_PROCESS_ROLES
          value: "broker,controller"
        - name: KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR
          value: "3"
        - name: KAFKA_NUM_PARTITIONS
          value: "3"
        - name: KAFKA_DEFAULT_REPLICATION_FACTOR
          value: "3"
        - name: KAFKA_MIN_INSYNC_REPLICAS
          value: "2"
        - name: KAFKA_CONTROLLER_LISTENER_NAMES
          value: "CONTROLLER"
        - name: KAFKA_LISTENERS
          value: PLAINTEXT://0.0.0.0:9092,CONTROLLER://0.0.0.0:29093,SSL://0.0.0.0:9093
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        name: kafka
        image: docker.io/confluentinc/cp-kafka:7.5.0
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 6
          initialDelaySeconds: 60
          periodSeconds: 60
          successThreshold: 1
          tcpSocket:
            port: tcp-kafka-int
          timeoutSeconds: 5
        ports:
        - containerPort: 9092
          name: tcp-kafka-int
          protocol: TCP
        - containerPort: 29093
          name: tcp-kafka-ctrl
          protocol: TCP
        - containerPort: 9093
          name: tcp-kafka-ssl
          protocol: TCP
        resources:
          limits:
            cpu: "1"
            memory: 1400Mi
          requests:
            cpu: 250m
            memory: 512Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsGroup: 1000
          runAsUser: 1000
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/kafka/secrets/
          name: kafka-ssl
        - mountPath: /etc/kafka
          name: config
        - mountPath: /var/lib/kafka/data
          name: data
        - mountPath: /var/log
          name: logs
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 1000
      terminationGracePeriodSeconds: 30
      volumes:
      - emptyDir: {}
        name: config
      - emptyDir: {}
        name: logs
      - name: kafka-ssl
        configMap: 
          name: kafka-ssl
  updateStrategy:
    type: RollingUpdate
  volumeClaimTemplates:
  - apiVersion: v1
    kind: PersistentVolumeClaim
    metadata:
      name: data
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: 10Gi
      storageClassName: standard
      volumeMode: Filesystem
    status:
      phase: Pending
