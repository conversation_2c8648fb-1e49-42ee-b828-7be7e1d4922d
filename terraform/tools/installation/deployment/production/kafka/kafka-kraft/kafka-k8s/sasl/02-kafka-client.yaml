---
apiVersion: v1
kind: Pod
metadata:
  name: kafka-cli
  namespace: kafka
  labels:
    app: kafka-cli
spec:
  containers:
  - name: kafka-cli
    command:
    - sh
    - -c
    - "trap : TERM INT; sleep infinity & wait"
    env:
    - name: BOOTSTRAP_SERVER
      value: kafka-0.kafka-headless.kafka.svc.cluster.local:9092,kafka-1.kafka-headless.kafka.svc.cluster.local:9092,kafka-2.kafka-headless.kafka.svc.cluster.local:9092
    image: docker.io/confluentinc/cp-kafka:7.5.0
    imagePullPolicy: IfNotPresent
    resources:
      limits:
        cpu: 500m
        memory: 500Mi
      requests:
        cpu: 500m
        memory: 500Mi
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    volumeMounts:
        - mountPath: /etc/kafka/secrets/
          name: kafka-client
  dnsPolicy: ClusterFirst
  enableServiceLinks: true
  restartPolicy: Always
  securityContext: {}
  serviceAccountName: kafka
  terminationGracePeriodSeconds: 30
  volumes:
  - name: kafka-client
    configMap: 
      name: kafka-client