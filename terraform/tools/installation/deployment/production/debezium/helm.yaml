helm upgrade --install debezium-platform debezium/debezium-platform \
  --version 3.1.0-final \
  --namespace debezium-platform \
  --set kafka.bootstrapServers=dbz-kafka-kafka-bootstrap.debezium-platform.svc:9092 \
  --set kafka.properties.security.protocol=PLAINTEXT \
  --set connect.config.storage.topic=debezium-configs \
  --set connect.offset.storage.topic=debezium-offsets \
  --set connect.status.storage.topic=debezium-statuses \
  --set connect.config.storage.replication.factor=3 \
  --set connect.offset.storage.replication.factor=3 \
  --set connect.status.storage.replication.factor=3 \
  --set connect.replicas=3 \
  --set service.type=ClusterIP \
  --set service.port=8083 \
  --set domain.url=debezium.snapgreen.world \
  --set database.type=postgresql \
  --set database.host=*********** \
  --set database.port=5432 \
  --set database.name=debezium \
  --set database.auth.existingSecret=debezium-db-auth \
  --set resources.requests.cpu=1000m \
  --set resources.requests.memory=2Gi \
  --set resources.limits.cpu=2000m \
  --set resources.limits.memory=4Gi \
  --set ui.enabled=true \
  --set ui.connect.uri=http://debezium-connect-connect-api.debezium-platform.svc:8083 \
  --set ui.connect.cluster=debezium-connect
