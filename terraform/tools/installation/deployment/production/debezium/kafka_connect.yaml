apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnect
metadata:
  name: debezium-connect
  namespace: kafka
  annotations:
    strimzi.io/use-connector-resources: "true"
spec:
  #image: asia-southeast2-docker.pkg.dev/snapgreen-prods/debezium/debezium-strimzi-connect:latest
  replicas: 1
  bootstrapServers: debezium-kafka-kafka-bootstrap:9092
  config:
    config.providers: secrets
    config.providers.secrets.class: io.strimzi.kafka.KubernetesSecretConfigProvider
    group.id: connect-cluster
    offset.storage.topic: connect-offsets
    config.storage.topic: connect-configs
    status.storage.topic: connect-status
    config.storage.replication.factor: 1
    offset.storage.replication.factor: 1
    status.storage.replication.factor: 1
    key.converter: org.apache.kafka.connect.json.JsonConverter
    value.converter: org.apache.kafka.connect.json.JsonConverter
    key.converter.schemas.enable: false
    value.converter.schemas.enable: false
  build:
    output:
      type: docker
      image: asia-southeast2-docker.pkg.dev/snapgreen-prods/debezium/debezium-connect-with-jdbc:latest # Custom image name
      pushSecret: gcp-artifact-registry-secret
    plugins:
      # Debezium PostgreSQL Source Plugin
      - name: debezium-postgres
        artifacts:
          - type: tgz
            url: https://repo1.maven.org/maven2/io/debezium/debezium-connector-postgres/2.5.0.Final/debezium-connector-postgres-2.5.0.Final-plugin.tar.gz
      # Confluent JDBC Sink Plugin
      - name: kafka-connect-jdbc
        artifacts:
          - type: zip
            url: https://storage.googleapis.com/kafka-plugin-jdbc/confluentinc-kafka-connect-jdbc-10.8.4.zip
  resources:
    requests:
      memory: 512Mi
      cpu: 250m
    limits:
      memory: 1Gi
      cpu: "0.5"
  template:
    pod:
      metadata:
        labels:
          app: debezium-connect
      securityContext:
        fsGroup: 1001
      imagePullSecrets:
        - name: gcp-artifact-registry-secret
      volumes:
        - name: db-secret-debezium
          secret:
            secretName: db-secret-debezium
    connectContainer:
      env:
        - name: user
          valueFrom:
            secretKeyRef:
              name: db-secret-debezium
              key: user
        - name: password
          valueFrom:
            secretKeyRef:
              name: db-secret-debezium
              key: password  
        - name: LOG_LEVEL
          value: "INFO"