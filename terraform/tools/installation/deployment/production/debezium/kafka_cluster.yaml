# ---------------------------------------------
# Kafka Cluster Definition (Strimzi)
# ---------------------------------------------
apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: debezium-kafka
  namespace: kafka
spec:
  kafka:
    version: 3.8.0
    replicas: 1
    listeners:
      - name: plain
        port: 9092
        type: internal
        tls: false
    config:
      offsets.topic.replication.factor: 1
      transaction.state.log.replication.factor: 1
      transaction.state.log.min.isr: 1
    storage:
      type: ephemeral
  zookeeper:
    replicas: 1
    storage:
      type: ephemeral
  entityOperator:
    topicOperator: {}
    userOperator: {}
