---
apiVersion: v1
kind: Namespace
metadata:
  name: debezium-platform
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaNodePool
metadata:
  name: broker-pool
  namespace: debezium-platform  # <-- Assigned to the Debezium namespace
  labels:
    strimzi.io/cluster: dbz-kafka
spec:
  replicas: 3
  roles:
    - broker
    - controller
  resources:
    requests:
      cpu: "0.25"  # Absolute minimum for testing
      memory: "1Gi"
    limits:
      cpu: "0.5"
      memory: "2Gi"
  storage:
    type: jbod
    volumes:
    - id: 0
      type: persistent-claim
      size: 50Gi
      class: kafka-storage
      deleteClaim: false
---
apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: dbz-kafka
  namespace: debezium-platform  # <-- Debezium namespace
  annotations:
    strimzi.io/node-pools: enabled
    strimzi.io/kraft: enabled
spec:
  kafka:
    version: 3.9.0
    config:
      node.id: "${STRIMZI_NODE_ID}"
      process.roles: "${STRIMZI_NODE_ROLES}"
      controller.quorum.voters: "${STRIMZI_CONTROLLER_QUORUM_VOTERS}"
      inter.broker.protocol.version: "3.9"
      log.message.format.version: "3.9"
    listeners:
      - name: plain
        port: 9092
        type: internal
        tls: false
      - name: tls
        port: 9093
        type: internal
        tls: true
    jvmOptions:
      -Xms: 2048m
      -Xmx: 2048m
  entityOperator:
    topicOperator:
      resources:
        requests:
          cpu: "200m"    # Reduced from 300m (minimum for stability)
          memory: "256Mi"
        limits:
          cpu: "300m"    # Reduced from 500m
          memory: "1Gi"
    userOperator:
      resources:
        requests:
          cpu: "200m"   # Reduced from 300m
          memory: "256Mi"
        limits:
          cpu: "300m"    # Reduced from 500m
          memory: "1Gi"
---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: kafka-storage
  namespace: debezium-platform  # <-- Optional: SCs are cluster-scoped but referenced in the namespace
provisioner: kubernetes.io/gce-pd
parameters:
  type: pd-standard
  fsType: ext4
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer
