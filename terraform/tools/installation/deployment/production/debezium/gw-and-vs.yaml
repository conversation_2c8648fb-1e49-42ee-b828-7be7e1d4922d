apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: debezium-platform-gateway
  namespace: debezium-platform
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "debezium.snapgreen.world"
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: wild-card-tls-prod
    hosts:
    - "debezium.snapgreen.world"

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: debezium-platform-vs
  namespace: debezium-platform
spec:
  hosts:
  - "debezium.snapgreen.world"
  gateways:
  - debezium-platform/debezium-platform-gateway
  http:
  - match:
    - uri:
        prefix: /api
    route:
    - destination:
        host: conductor.debezium-platform.svc.cluster.local
        port:
          number: 8080
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: stage.debezium-platform.svc.cluster.local
        port:
          number: 3000