apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnect
metadata:
  name: debezium-connect
  namespace: debezium-platform  # Changed to match your existing namespace
  annotations:
    strimzi.io/use-connector-resources: "true"
spec:
  #version: 3.6.0  # Added explicit Kafka version
  replicas: 1
  bootstrapServers: dbz-kafka-kafka-bootstrap.debezium-platform.svc:9092  # Updated to match your service
  config:
    config.providers: secrets
    config.providers.secrets.class: io.strimzi.kafka.KubernetesSecretConfigProvider
    group.id: connect-cluster
    offset.storage.topic: connect-offsets
    config.storage.topic: connect-configs
    status.storage.topic: connect-status
    # Increased replication factors for production
    config.storage.replication.factor: 3  
    offset.storage.replication.factor: 3
    status.storage.replication.factor: 3
    key.converter: org.apache.kafka.connect.json.JsonConverter
    value.converter: org.apache.kafka.connect.json.JsonConverter
    key.converter.schemas.enable: false
    value.converter.schemas.enable: false
    # Additional recommended settings
    heartbeat.interval.ms: 10000
    session.timeout.ms: 30000
  build:
    output:
      type: docker
      image: asia-southeast2-docker.pkg.dev/snapgreen-prods/debezium/debezium-connect-with-jdbc:latest
      pushSecret: gcp-artifact-registry-secret
    plugins:
      - name: debezium-postgres
        artifacts:
          - type: tgz
            url: https://repo1.maven.org/maven2/io/debezium/debezium-connector-postgres/2.5.0.Final/debezium-connector-postgres-2.5.0.Final-plugin.tar.gz
      - name: kafka-connect-jdbc
        artifacts:
          - type: zip
            url: https://storage.googleapis.com/kafka-plugin-jdbc/confluentinc-kafka-connect-jdbc-10.8.4.zip
  resources:
    requests:
      memory: 1Gi  # Increased minimum memory
      cpu: 500m
    limits:
      memory: 2Gi
      cpu: "1"
  template:
    pod:
      metadata:
        labels:
          app: debezium-connect
      securityContext:
        fsGroup: 1001
      imagePullSecrets:
        - name: gcp-artifact-registry-secret
      volumes:
        - name: db-secret-debezium
          secret:
            secretName: db-secret-debezium
    connectContainer:
      env:
        - name: user
          valueFrom:
            secretKeyRef:
              name: db-secret-debezium
              key: user
        - name: password
          valueFrom:
            secretKeyRef:
              name: db-secret-debezium
              key: password  
        - name: LOG_LEVEL
          value: "DEBUG"  # More verbose for troubleshooting