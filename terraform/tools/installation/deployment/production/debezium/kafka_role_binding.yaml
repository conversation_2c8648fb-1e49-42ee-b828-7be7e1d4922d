apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: debezium-secret-reader
  namespace: kafka
rules:
- apiGroups: [""]
  resources: ["secrets"]
  resourceNames: ["db-secret-debezium"]
  verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: debezium-secret-reader-binding
  namespace: kafka
subjects:
- kind: ServiceAccount
  name: debezium-connect-connect  # e.g., "debezium-connect-cluster-connect"
  namespace: kafka
roleRef:
  kind: Role
  name: debezium-secret-reader
  apiGroup: rbac.authorization.k8s.io