apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: monitoring-gateway
  namespace: monitoring
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "grafana-monitoring.snapgreen.world"
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: wild-card-tls-prod
    hosts:
    - "grafana-monitoring.snapgreen.world"

---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: grafana
  namespace: monitoring
spec:
  hosts:
  - "grafana-monitoring.snapgreen.world"
  gateways:
  - monitoring/monitoring-gateway
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: grafana.monitoring.svc.cluster.local
        port:
          number: 80
