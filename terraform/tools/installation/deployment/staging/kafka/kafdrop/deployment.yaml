apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafdrop
  namespace: kafka
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafdrop
  template:
    metadata:
      labels:
        app: kafdrop
    spec:
      containers:
        - name: kafdrop
          image: obsidiandynamics/kafdrop:4.0.2
          ports:
            - containerPort: 9000
          env:
            - name: KAFKA_BROKER_CONNECT
              value: "kafka-0.kafka-headless.kafka.svc.cluster.local:9092" # Replace with your Kafka broker address
            - name: KAFKA_CLIENT_TIMEOUT
              value: "60s"  
---
apiVersion: v1
kind: Service
metadata:
  name: kafdrop
  namespace: kafka
spec:
  ports:
    - port: 9000
      targetPort: 9000
  selector:
    app: kafdrop
  type: ClusterIP
