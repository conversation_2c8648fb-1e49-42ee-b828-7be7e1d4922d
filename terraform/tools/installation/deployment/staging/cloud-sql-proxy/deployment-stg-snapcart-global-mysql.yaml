apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloud-sql-proxy
  namespace: cloud-sql-proxy
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cloud-sql-proxy
  template:
    metadata:
      labels:
        app: cloud-sql-proxy
    spec:
      containers:
      - name: cloud-sql-proxy
        image: gcr.io/cloudsql-docker/gce-proxy:1.33.1
        command:
          - "/cloud_sql_proxy"
          - "--instances=endgame-dc-stage-6bzu:asia-southeast2:dc-id=tcp:3306"  # Change port to MySQL (3306)
          - "--verbose"  # Add verbosity for debugging
        ports:
        - containerPort: 3306
        env:
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: /secrets/key.json
        volumeMounts:
        - name: cloudsql-instance-credentials
          mountPath: /secrets
          readOnly: true
      volumes:
      - name: cloudsql-instance-credentials
        secret:
          secretName: my-cloud-sql-credentials
---
apiVersion: v1
kind: Service
metadata:
  name: cloud-sql-proxy
  namespace: cloud-sql-proxy
spec:
  selector:
    app: cloud-sql-proxy  # This should match the labels in your deployment
  ports:
    - name: tcp
      port: 3306  # Change to MySQL default port
      targetPort: 3306
      protocol: TCP
