apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloud-sql-proxy
  namespace: cloud-sql-proxy
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cloud-sql-proxy
  template:
    metadata:
      labels:
        app: cloud-sql-proxy
    spec:
      containers:
      - name: cloud-sql-proxy
        image: gcr.io/cloudsql-docker/gce-proxy:1.33.1
        command:
          - "/cloud_sql_proxy"
          - "--instances=endgame-dc-stage-6bzu:asia-southeast2:ingress-kong-stage=tcp:5432"  # PostgreSQL
          - "--instances=endgame-dc-stage-6bzu:asia-southeast2:dc-id=tcp:3306"  # MySQL
          - "--verbose"  # Enables detailed logging
        ports:
        - containerPort: 5432  # PostgreSQL
        - containerPort: 3306  # MySQL
        env:
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: /secrets/key.json
        volumeMounts:
        - name: cloudsql-instance-credentials
          mountPath: /secrets
          readOnly: true
      volumes:
      - name: cloudsql-instance-credentials
        secret:
          secretName: my-cloud-sql-credentials
---
apiVersion: v1
kind: Service
metadata:
  name: cloud-sql-proxy
  namespace: cloud-sql-proxy
spec:
  selector:
    app: cloud-sql-proxy  # This should match the labels in your deployment
  ports:
    - name: postgresql
      port: 5432
      targetPort: 5432
      protocol: TCP
    - name: mysql
      port: 3306
      targetPort: 3306
      protocol: TCP
