apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloud-sql-proxy
  namespace: cloud-sql-proxy
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cloud-sql-proxy
  template:
    metadata:
      labels:
        app: cloud-sql-proxy
    spec:
      containers:
      - name: cloud-sql-proxy
        image: gcr.io/cloudsql-docker/gce-proxy:1.33.1
        command:
          - "/cloud_sql_proxy"
          - "--instances=snapgreen-prods:asia-southeast2:snapgreen-prods-db=tcp:5432"  # Add the instance flag here
        ports:
        - containerPort: 5432
        env:
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: /secrets/key.json
        volumeMounts:
        - name: cloudsql-instance-credentials
          mountPath: /secrets
          readOnly: true
      volumes:
      - name: cloudsql-instance-credentials
        secret:
          secretName: my-cloud-sql-credentials
---
apiVersion: v1
kind: Service
metadata:
  name: cloud-sql-proxy
  namespace: cloud-sql-proxy
spec:
  selector:
    app: cloud-sql-proxy  # This should match the labels in your cloud-sql-proxy deployment
  ports:
    - name: tcp
      port: 5432
      targetPort: 5432
      protocol: TCP





# kubectl create secret generic my-cloud-sql-credentials \
#   --from-file=key.json=cloud-sql-prod.json \
#   --namespace=cloud-sql-proxy

