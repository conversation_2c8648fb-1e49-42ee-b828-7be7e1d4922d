apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: snapgreen-domain-org
  namespace: istio-system
spec:
  secretName: wild-card-tls-stg
  commonName: "*.snapgreen.org"
  isCA: false
  usages:
    - server auth
    - client auth
  duration: 2160h # 90 days, Let's Encrypt maximum duration
  renewBefore: 360h # 15 days before expiration
  dnsNames:
  - "*.snapgreen.org"
  issuerRef:
    name:  letsencrypt-stg-snap
    kind: ClusterIssuer