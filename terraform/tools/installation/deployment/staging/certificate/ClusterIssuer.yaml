apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-stg-snap
  namespace: cert-manager
spec:
  acme:
    email: luk<PERSON>@snapgreen.world # Replace with your email
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: letsencrypt-stg-private-key
    solvers:
    - dns01:
        cloudDNS:
          project: snapgreen-stg # Replace with your GCP project ID
          hostedZoneName: snapgreen-org  # Replace with your DNS zone name
          serviceAccountSecretRef:
            name: clouddns-secret
            key: clouddns-key.json

            
# "egaTJN382THLpF3jxkjP3Gl4hd2NsOmXvzpkKjS0-9Y"
