#!/bin/bash

# Slack Webhook URL (Not used, left here for future use)
SLACK_WEBHOOK_URL="*******************************************************************************"

# Function to check and install kubectl
check_and_install_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        echo "kubectl not found. Installing kubectl..."
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
        chmod +x kubectl
        sudo mv kubectl /usr/local/bin/
        echo "kubectl installed successfully."
    else
        echo "kubectl is already installed."
    fi
}

# Function to select environment
select_environment() {
    echo "Select an environment:"
    echo "1) Production"
    echo "2) Staging"
    echo "3) Quit"
    read -p "Enter your choice [1-3]: " env_choice

    case $env_choice in
        1)
            CURRENT_PROJECT="snapgreen-prods"
            CLUSTER="gke-prod-cluster"
            REGION="asia-southeast2"
            ;;
        2)
            CURRENT_PROJECT="snapgreen-stg"
            CLUSTER="gke-stg-cluster"
            REGION="asia-southeast2"
            ;;
        3)
            echo "Exiting script. Goodbye!"
            exit 0
            ;;
        *)
            echo "Invalid choice. Exiting."
            exit 1
            ;;
    esac
}

# Function to select namespace
select_namespace() {
    echo "Select a namespace:"
    echo "1) snapgreen-retail-app"
    echo "2) snapgreen-marvel-app"
    echo "3) Quit"
    read -p "Enter your choice [1-3]: " namespace_choice

    case $namespace_choice in
        1)
            NAMESPACE="snapgreen-retail-app"
            SERVICES=("retail-be-api" "retail-dashboard" "retail-fe")
            ;;
        2)
            NAMESPACE="snapgreen-marvel-app"
            SERVICES=("marvel-dashboard" "post-order-api" "post-order-payment" "pre-order-api" "pre-order-callback-worker" "pre-order-cron" "pre-order-import-worker" "snapdocs")
            ;;
        3)
            echo "Exiting script. Goodbye!"
            exit 0
            ;;
        *)
            echo "Invalid choice. Exiting."
            exit 1
            ;;
    esac
}

# Function to select service
select_service() {
    while true; do
        echo "Select a service monitor logs:"
        for i in "${!SERVICES[@]}"; do
            echo "$((i + 1))) ${SERVICES[i]}"
        done
        echo "$(( ${#SERVICES[@]} + 1 ))) Go back to namespace selection"
        echo "$(( ${#SERVICES[@]} + 2 ))) Exit to environment selection"
        echo "$(( ${#SERVICES[@]} + 3 ))) Quit"
        read -p "Enter your choice [1-$(( ${#SERVICES[@]} + 3 ))]: " service_choice

        if [[ $service_choice -ge 1 && $service_choice -le ${#SERVICES[@]} ]]; then
            SERVICE_TO_MONITOR="${SERVICES[$((service_choice - 1))]}"
            monitor_logs_for_service "$CURRENT_PROJECT" "$CLUSTER" "$REGION" "$NAMESPACE" "$SERVICE_TO_MONITOR"
        elif [[ $service_choice -eq $(( ${#SERVICES[@]} + 1 )) ]]; then
            select_namespace
        elif [[ $service_choice -eq $(( ${#SERVICES[@]} + 2 )) ]]; then
            select_environment
            select_namespace
        elif [[ $service_choice -eq $(( ${#SERVICES[@]} + 3 )) ]]; then
            echo "Exiting script. Goodbye!"
            exit 0
        else
            echo "Invalid choice. Try again."
        fi
    done
}

# Function to monitor logs for a specific service
monitor_logs_for_service() {
    GCP_PROJECT=$1
    CLUSTER=$2
    REGION=$3
    NAMESPACE=$4
    SERVICE=$5

    echo -e "\033[1;34mMonitoring GCP Project: $GCP_PROJECT, Cluster: $CLUSTER, Namespace: $NAMESPACE for service: $SERVICE\033[0m"

    # Authenticate to the GKE cluster
    if ! gcloud container clusters get-credentials "$CLUSTER" --project="$GCP_PROJECT" --region="$REGION"; then
        echo -e "\033[1;31mFailed to authenticate with cluster '$CLUSTER'. Skipping namespace '$NAMESPACE'.\033[0m"
        return
    fi

    # Get all pod names related to the specified service
    PODS=$(kubectl get pods -n "$NAMESPACE" --no-headers | grep -i "$SERVICE" | awk '{print $1}')

    if [ -z "$PODS" ]; then
        echo -e "\033[1;33mNo pods found for service '$SERVICE' in namespace '$NAMESPACE'.\033[0m"
        return
    fi

    for POD in $PODS; do
        echo -e "\033[1;34mFetching logs for pod '$POD' in namespace '$NAMESPACE'...\033[0m"

        # Fetch logs and filter for relevant keywords
        LOGS=$(kubectl logs "$POD" -n "$NAMESPACE" | grep -Ei "error|failed|connection timed out")

        if [ -z "$LOGS" ]; then
            echo -e "\033[1;33mNo relevant logs found for pod '$POD'.\033[0m"
            continue
        fi

        # Escape special characters in logs for clean output
        ESCAPED_LOGS=$(echo "$LOGS" | sed 's/"/\\"/g' | sed 's/%/%%/g')

        # Truncate logs if they exceed terminal-friendly limit (optional)
        MAX_LENGTH=3500  # Leave room for metadata
        if [ ${#ESCAPED_LOGS} -gt $MAX_LENGTH ]; then
            ESCAPED_LOGS="${ESCAPED_LOGS:0:$MAX_LENGTH}... (truncated)"
        fi

        # Print logs to the terminal
        echo -e "\033[1;32m--------------------------------------------------------\033[0m"
        echo -e "\033[1;32mLogs for Pod: $POD\033[0m"
        echo -e "\033[1;32mNamespace: $NAMESPACE\033[0m"
        echo -e "\033[1;32m--------------------------------------------------------\033[0m"
        echo "$ESCAPED_LOGS"
        echo -e "\033[1;32m--------------------------------------------------------\033[0m"
    done
}

# Main Script Execution
check_and_install_kubectl

# Select environment
select_environment

# Select namespace
select_namespace

# Select service
select_service
