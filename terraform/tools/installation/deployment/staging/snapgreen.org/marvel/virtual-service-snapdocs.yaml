apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: marvel-snapdocs-vs
  namespace: snapgreen-marvel-app
spec:
  hosts:
  - "snapdocs.snapgreen.org"  # Specific subdomain
  gateways:
  - snapgreen-marvel-app/marvel-gateway-snapgreen-org  # Update to correct namespace
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: snapdocs-svc.snapgreen-marvel-app.svc.cluster.local
        port:
          number: 80
