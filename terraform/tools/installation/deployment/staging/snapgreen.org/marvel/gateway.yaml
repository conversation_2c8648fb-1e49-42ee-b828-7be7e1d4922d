apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: marvel-gateway-snapgreen-org
  namespace: snapgreen-marvel-app
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "marvel-post-order.snapgreen.org"
    - "marvel-pre-order.snapgreen.org"
    - "marvel-dashboard.snapgreen.org"
    - "snapdocs.snapgreen.org"
    - "kratos-api.snapgreen.org"
    - "keto-read.snapgreen.org"
    - "keto-write.snapgreen.org"
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: wild-card-tls-stg
    hosts:
    - "marvel-post-order.snapgreen.org"
    - "marvel-pre-order.snapgreen.org"
    - "marvel-dashboard.snapgreen.org"
    - "snapdocs.snapgreen.org"
    - "kratos-api.snapgreen.org"
    - "keto-read.snapgreen.org"
    - "keto-write.snapgreen.org"
  - port:
      number: 8081
      name: grpc
      protocol: HTTP2
    hosts:
    - "marvel-post-order.snapgreen.org"
    - "marvel-pre-order.snapgreen.org"
    - "marvel-dashboard.snapgreen.org"
    - "snapdocs.snapgreen.org"
    - "keto-read.snapgreen.org"
    - "keto-write.snapgreen.org"
