apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: retail-fe-vs
  namespace: snapgreen-retail-app
spec:
  hosts:
  - "retail-fe.snapgreen.org"  # Specific subdomain
  gateways:
  - snapgreen-retail-app/retail-gateway-snapgreen-org  # Update to correct namespace
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: retail-fe-svc.snapgreen-retail-app.svc.cluster.local
        port:
          number: 3000
