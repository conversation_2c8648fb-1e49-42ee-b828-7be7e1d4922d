apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: retail-gateway-snapgreen-org
  namespace: snapgreen-retail-app
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "retail-dashboard.snapgreen.org"
    - "retail-fe.snapgreen.org"
    - "retail-be.snapgreen.org"
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: wild-card-tls-stg
    hosts:
    - "retail-dashboard.snapgreen.org"
    - "retail-fe.snapgreen.org"
    - "retail-be.snapgreen.org"