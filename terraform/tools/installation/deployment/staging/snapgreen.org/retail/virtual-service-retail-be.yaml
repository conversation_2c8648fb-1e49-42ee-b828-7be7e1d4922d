apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: retail-be-vs
  namespace: snapgreen-retail-app
spec:
  hosts:
  - "retail-be.snapgreen.org"  # Specific subdomain
  gateways:
  - snapgreen-retail-app/retail-gateway-snapgreen-org  # Update to correct namespace
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: retail-be-svc.snapgreen-retail-app.svc.cluster.local
        port:
          number: 8000
