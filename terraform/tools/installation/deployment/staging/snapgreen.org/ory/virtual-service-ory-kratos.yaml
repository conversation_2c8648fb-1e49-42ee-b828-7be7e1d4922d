apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: kratos-virtual-service
  namespace: kratos
spec:
  hosts:
    - "kratos-api.snapgreen.org"
  gateways:
    - snapgreen-marvel-app/marvel-gateway-snapgreen-org
  http:
    - match:
        - uri:
            prefix: /
      # headers:
      #   response:     
      #     add: 
      #       access-control-allow-origin: https://marvel-dashboard.snapgreen.org
      #       access-control-allow-credentials: "true"
      #       access-control-expose-headers: Content-Type
      route:
        - destination:
            host: kratos-public.kratos.svc.cluster.local
            port:
              number: 80
    