apiVersion: batch/v1
kind: Job
metadata:
  name: kratos-migrate
  namespace: kratos
spec:
  template:
    spec:
      containers:
      - name: kratos-migrate
        image: oryd/kratos:v0.13.0
        command: ["kratos", "migrate", "sql", "-e", "--yes"]
        env:
        - name: DSN
          valueFrom:
            secretKeyRef:
              name: kratos-secrets
              key: DSN
      restartPolicy: OnFailure
