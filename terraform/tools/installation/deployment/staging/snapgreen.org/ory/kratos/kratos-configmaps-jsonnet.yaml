apiVersion: v1
kind: ConfigMap
metadata:
  name: kratos-jsonnet-config
  namespace: kratos
data:
  oidc.google.jsonnet: |
    local claims = {
      email_verified: true,
    } + std.extVar('claims');

    {
      identity: {
        traits: {
          [if 'email' in claims && claims.email_verified then 'email' else null]: claims.email,
          [if 'given_name' in claims then 'name' else null]: {
            first: claims.given_name,
            last: claims.family_name,
          },
        },
      },
    }

  assign-role-body.jsonnet: |
    function(ctx) { user_id: ctx.identity.id, role: "admin" }
