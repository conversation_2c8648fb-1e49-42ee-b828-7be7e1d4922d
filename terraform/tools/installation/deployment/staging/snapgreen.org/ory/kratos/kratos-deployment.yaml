apiVersion: apps/v1
kind: Deployment
metadata:
  name: kratos
  namespace: kratos
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kratos
  template:
    metadata:
      labels:
        app: kratos
    spec:
      containers:
      - name: kratos
        image: oryd/kratos:v1.3.0
        command: ["kratos", "serve", "--config", "/etc/config/kratos.yaml"]
        env:
        - name: DSN
          valueFrom:
            secretKeyRef:
              name: kratos-secrets
              key: DSN
        - name: CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: kratos-secrets
              key: CLIENT_ID
        - name: CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: kratos-secrets
              key: CLIENT_SECRET
        - name: COOKIE_SECRET
          valueFrom:
            secretKeyRef:
              name: kratos-secrets
              key: COOKIE_SECRET
        volumeMounts:
        - name: config-volume
          mountPath: /etc/config/kratos.yaml
          subPath: kratos.yaml
        - name: schema-volume
          mountPath: /etc/config/identity.schema.json
          subPath: identity.schema.json
        - name: jsonnet-volume
          mountPath: /etc/config/oidc.google.jsonnet
          subPath: oidc.google.jsonnet
        - name: jsonnet-volume
          mountPath: /etc/config/assign-role-body.jsonnet
          subPath: assign-role-body.jsonnet
      volumes:
      - name: config-volume
        configMap:
          name: kratos-config
      - name: schema-volume
        configMap:
          name: kratos-identity-schema
      - name: jsonnet-volume
        configMap:
          name: kratos-jsonnet-config
---

apiVersion: v1
kind: Service
metadata:
  name: kratos-public
  namespace: kratos
spec:
  selector:
    app: kratos
  ports:
    - protocol: TCP
      port: 80
      targetPort: 4433
---

apiVersion: v1
kind: Secret
metadata:
  name: kratos-secrets
  namespace: kratos
type: Opaque
data:
  DSN: ************************************************************************************************
  CLIENT_ID: ZDFUclZLMFJGZTZUb3M4UkNSRVJMYQ== # d1TrVK0RFe6Tos8RCRERLa
  CLIENT_SECRET: bUtVeEc0ZE4zWjhGV3RLeTRHSGI4eg== # mKUxG4dN3Z8FWtKy4GHb8z
  COOKIE_SECRET: YTlmNUI3U2loaGRsTTUwN1c0bmRTcg== # a9f5B7SihhdlM507W4ndSr
  CIPHER_SECRET: a0RiWllhSERXZldXam1YaktDc3VjWVJSb3RmWEtDTUE= #  kDbZYaHDWfWWjmXjKCsucYRRotfXKCMA
  INTERNAL_CODE: QnkzV2Jub3g0MFQxS3NzT3lqOU10MEJqNlBsbw== # By3Wbnox40T1KssOyj9Mt0Bj6Plo

---

apiVersion: batch/v1
kind: Job
metadata:
  name: kratos-migrate
  namespace: kratos
spec:
  template:
    spec:
      containers:
      - name: kratos-migrate
        image: oryd/kratos:v0.13.0
        command: ["kratos", "migrate", "sql", "-e", "--yes"]
        env:
        - name: DSN
          valueFrom:
            secretKeyRef:
              name: kratos-secrets
              key: DSN
      restartPolicy: OnFailure
