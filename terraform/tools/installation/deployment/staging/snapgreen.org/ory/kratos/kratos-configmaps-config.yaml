apiVersion: v1
kind: ConfigMap
metadata:
  name: kratos-config
  namespace: kratos
data:
  kratos.yaml: |
    version: v0.13.0

    dsn: ${DSN}

    serve:
      public:
        base_url: https://kratos-api.snapgreen.org
        cors:
          enabled: true
          allowed_origins:
            - https://*.snapgreen.org
            - http://*.snapgreen.org
          allowed_methods:
            - POST
            - GET
            - PUT
            - PATCH
            - DELETE
          allowed_headers:
            - Authorization
            - Content-Type
            - Access-Control-Allow-Credentials
            - Access-Control-Allow-Origin
            - Access-Control-Expose-Headers
            - Cookie
          

          exposed_headers:
            - Content-Type
            - Set-Cookie
          allow_credentials: true

    selfservice:
      default_browser_return_url: https://marvel-dashboard.snapgreen.org
      allowed_return_urls:
        - https://marvel-dashboard.snapgreen.org/dashboard
        - http://marvel-dashboard.snapgreen.org/dashboard
        - http://localhost:4455
        - http://localhost:19006/Callback
        - exp://localhost:8081/--/Callback

      methods:
        password:
          enabled: true
        totp:
          enabled: true
          config:
            issuer: <PERSON><PERSON><PERSON>
        lookup_secret:
          enabled: true
        link:
          enabled: true
        code:
          enabled: true
        oidc:
          enabled: true
          config:
            providers:
              - id: google
                provider: google
                client_id: ************-rp5o9og9hfjmdejq64p31tikbi25beii.apps.googleusercontent.com
                client_secret: 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
                issuer_url: https://accounts.google.com
                mapper_url: file:///etc/config/oidc.google.jsonnet
                scope:
                  - email
                  - profile

      flows:
        login:
          ui_url: https://marvel-dashboard.snapgreen.org/login
        registration:
          ui_url: https://marvel-dashboard.snapgreen.org/registration
          after:
            oidc:
              hooks:
                - hook: session
                - hook: web_hook
                  config:
                    url: http://pre-order-svc.snapgreen-marvel-app.svc.cluster.local:8080/api/v1/user/assign-role
                    method: POST
                    body: file:///etc/config/assign-role-body.jsonnet
                    response:
                      ignore: true
                      parse: false
                    headers:
                      name: Content-Type
                      value: application/json
                      X-Internal-Code: "01JBEZX0BYD4SG14MYWVZ27ZJ1"
                      X-Internal-Request: "true"

        settings:
          ui_url: https://marvel-dashboard.snapgreen.org/settings
        recovery:
          ui_url: https://marvel-dashboard.snapgreen.org/recovery
        verification:
          ui_url: https://marvel-dashboard.snapgreen.org/verification
        error:
          ui_url: https://marvel-dashboard.snapgreen.org/error

    log:
      level: debug
      format: text
      leak_sensitive_values: true

    secrets:
      cookie:
        - ${COOKIE_SECRET}
      cipher:
        - kDbZYaHDWfWWjmXjKCsucYRRotfXKCMA

    ciphers:
      algorithm: xchacha20-poly1305

    hashers:
      algorithm: bcrypt
      bcrypt:
        cost: 8

    identity:
      default_schema_id: default
      schemas:
        - id: default
          url: file:///etc/config/identity.schema.json

    cookies:
      domain: .snapgreen.org
      path: /                   
      same_site: Lax      
          
    courier:
      smtp:
        connection_uri: smtps://test:test@mailslurper:1025/?skip_ssl_verify=true