apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: ory-gateway
  namespace: snapgreen-marvel-app
spec:
  selector:
    istio: ingressgateway  # Ensure the Istio ingress gateway is selected
  servers:
    - port:
        number: 80
        name: http
        protocol: HTTP
      hosts:
        - "kratos-api.snapgreen.org"
      tls:
        httpsRedirect: true          
    - port:
        number: 443
        name: https
        protocol: HTTPS
      tls:
        mode: SIMPLE
        credentialName: wild-card-tls-stg  # Kubernetes Secret with the wildcard TLS cert
      hosts:
        - "kratos-api.snapgreen.org"