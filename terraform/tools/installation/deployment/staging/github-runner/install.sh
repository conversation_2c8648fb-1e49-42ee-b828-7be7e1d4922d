#!/bin/bash
helm upgrade --install --namespace actions-runner-system --create-namespace \
  --set=authSecret.create=true \
  --set=authSecret.github_token="" \
  --set="githubWebhookServer.enabled=true" \
  --set="githubWebhookServer.secret.enabled=true" \
  --set="githubWebhookServer.secret.create=true" \
  --set="githubWebhookServer.secret.github_webhook_secret_token=" \
  --wait actions-runner-controller \
  actions-runner-controller/actions-runner-controller