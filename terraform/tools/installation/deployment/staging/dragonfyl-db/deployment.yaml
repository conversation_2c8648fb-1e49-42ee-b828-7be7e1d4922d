apiVersion: apps/v1
kind: Deployment
metadata:
  name: dragonfly-db
  namespace: dragonflydb  
  labels:
    app: dragonfly-db
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dragonfly-db
  template:
    metadata:
      labels:
        app: dragonfly-db
    spec:
      containers:
      - name: dragonfly-db
        image: docker.dragonflydb.io/dragonflydb/dragonfly:latest
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "500Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: dragonfly-db-service
  namespace: dragonflydb  
  labels:
    app: dragonfly-db
spec:
  ports:
    - port: 6379
      targetPort: 6379
  selector:
    app: dragonfly-db
  type: ClusterIP
