apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: hydra-ingress
  namespace: hydra
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"  # Reference the Let's Encrypt ClusterIssuer
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: hydra.snapcart.asia # Replace with your actual domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ory-hydra-public
            port:
              number: 4444  # Hydra service port
  tls:
  - hosts:
    - hydra.snapcart.asia
    secretName: hydra-cert-tls  # The TLS secret to store the certificate
