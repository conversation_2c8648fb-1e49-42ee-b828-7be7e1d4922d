#!/bin/bash
helm upgrade --install cert-manager jetstack/cert-manager \
    --namespace cert-manager \
    --create-namespace \
    --set installCRDs=true \
     --set extraArgs="{--cluster-issuer-ambient-credentials,--dns01-recursive-nameservers-only,--dns01-recursive-nameservers=10.3.3.6:53\,10.3.4.5:53}"


     

# 9 january 2025
    helm upgrade --install cert-manager jetstack/cert-manager \
    --namespace cert-manager \
    --create-namespace \
    --set installCRDs=true \
     --set extraArgs="{--cluster-issuer-ambient-credentials,--dns01-recursive-nameservers-only,--dns01-recursive-nameservers=kube-dns.kube-system.svc.cluster.local:53}"
 