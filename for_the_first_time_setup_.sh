#!/bin/bash

# Define base directories and files
BASE_DIR="terraform"
MODULES_DIR="$BASE_DIR/modules"
STACKS_DIR="$BASE_DIR/stacks"
MODULES=("network" "gke" "vpc_peering")
FILES=("main.tf" "variables.tf" "outputs.tf")
STACKS=("prod" "stg" "infra")
STACK_FILE="terramate.tm.hcl"

# Create base directory if it doesn't exist
mkdir -p "$BASE_DIR"
echo "Created base directory: $BASE_DIR"

# Create modules directory and its subdirectories
mkdir -p "$MODULES_DIR"
echo "Created modules directory: $MODULES_DIR"

for module in "${MODULES[@]}"; do
    MODULE_PATH="$MODULES_DIR/$module"
    
    # Create the module directories
    mkdir -p "$MODULE_PATH"
    echo "Created module directory: $MODULE_PATH"
done

# Create specific project directories (snapgreen-prod, snapgreen-staging, snapgreen-infra) and the necessary files
PROJECTS=("snapgreen-prod" "snapgreen-staging" "snapgreen-infra")

for project in "${PROJECTS[@]}"; do
    PROJECT_PATH="$BASE_DIR/$project"
    
    # Create the project directory
    mkdir -p "$PROJECT_PATH"
    echo "Created project directory: $PROJECT_PATH"
    
    # Create the files inside each project folder
    for file in "${FILES[@]}"; do
        touch "$PROJECT_PATH/$file"
        echo "# $file file for the $project project" > "$PROJECT_PATH/$file"
        echo "Created file: $PROJECT_PATH/$file"
    done
done

# Create stacks directory and subdirectories for Terramate
mkdir -p "$STACKS_DIR"
echo "Created stacks directory: $STACKS_DIR"

for stack in "${STACKS[@]}"; do
    STACK_PATH="$STACKS_DIR/$stack"
    
    # Create the stack directory
    mkdir -p "$STACK_PATH"
    echo "Created stack directory: $STACK_PATH"
    
    # Create terramate.tm.hcl file inside each stack directory
    touch "$STACK_PATH/$STACK_FILE"
    echo "# terramate.tm.hcl for $stack stack" > "$STACK_PATH/$STACK_FILE"
    echo "Created file: $STACK_PATH/$STACK_FILE"
done

echo "Terraform and Terramate project structure created successfully!"
